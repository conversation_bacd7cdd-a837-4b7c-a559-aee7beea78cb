import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { User } from '@/types';

interface AuthState {
    user: User | null;
    isAuthenticated: boolean;
    isLoading: boolean;
    login: (email: string, password: string) => Promise<void>;
    logout: () => void;
    toggleUserType: () => void;
}

// Mock user data
const mockUsers = {
    customer: {
        id: 'u4',
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '************',
        address: '123 Main St, Anytown, USA',
        isCook: false,
    },
    cook: {
        id: 'u1',
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '************',
        address: '456 Culinary Ave, Cooktown, USA',
        isCook: true,
    }
};

export const useAuthStore = create<AuthState>()(
    persist(
        (set) => ({
            user: null,
            isAuthenticated: false,
            isLoading: false,

            login: async (email, password) => {
                set({ isLoading: true });

                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Simple mock authentication
                if (email === '<EMAIL>' && password === 'password') {
                    set({
                        user: mockUsers.customer,
                        isAuthenticated: true,
                        isLoading: false
                    });
                } else if (email === '<EMAIL>' && password === 'password') {
                    set({
                        user: mockUsers.cook,
                        isAuthenticated: true,
                        isLoading: false
                    });
                } else {
                    set({ isLoading: false });
                    throw new Error('Invalid credentials');
                }
            },

            logout: () => {
                set({ user: null, isAuthenticated: false });
            },

            // For demo purposes - toggle between customer and cook
            toggleUserType: () => {
                set((state) => ({
                    user: state.user?.isCook ? mockUsers.customer : mockUsers.cook,
                }));
            }
        }),
        {
            name: 'auth-storage',
            storage: createJSONStorage(() => AsyncStorage),
        }
    )
);