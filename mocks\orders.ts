import { Order, OrderStatus } from '@/types';

export const orders: Order[] = [
    {
        id: 'o1',
        userId: 'u4',
        cookId: '1',
        items: [
            {
                mealId: '1',
                name: "Homemade Lasagna",
                price: 12.99,
                quantity: 2
            }
        ],
        status: 'delivered',
        total: 25.98,
        deliveryAddress: "123 Main St, Anytown, USA",
        deliveryFee: 3.99,
        placedAt: "2025-06-01T14:30:00Z",
        estimatedDelivery: "2025-06-01T15:30:00Z"
    },
    {
        id: 'o2',
        userId: 'u4',
        cookId: '2',
        items: [
            {
                mealId: '2',
                name: "Vegetable Curry",
                price: 10.99,
                quantity: 1
            },
            {
                mealId: '5',
                name: "Hearty Beef Stew",
                price: 13.99,
                quantity: 1
            }
        ],
        status: 'preparing',
        total: 24.98,
        deliveryAddress: "123 Main St, Anytown, USA",
        deliveryFee: 3.99,
        placedAt: "2025-06-05T12:15:00Z",
        estimatedDelivery: "2025-06-05T13:15:00Z",
        specialInstructions: "Please make the curry mild spicy."
    },
    {
        id: 'o3',
        userId: 'u5',
        cookId: '3',
        items: [
            {
                mealId: '3',
                name: "Homestyle Fried Chicken",
                price: 14.99,
                quantity: 3
            }
        ],
        status: 'confirmed',
        total: 44.97,
        deliveryAddress: "456 Oak Ave, Somewhere, USA",
        deliveryFee: 4.99,
        placedAt: "2025-06-05T11:45:00Z",
        estimatedDelivery: "2025-06-05T12:45:00Z",
        specialInstructions: "Extra crispy please!"
    }
];

export const cookOrders: Order[] = [
    {
        id: 'co1',
        userId: 'u6',
        cookId: '1',
        items: [
            {
                mealId: '1',
                name: "Homemade Lasagna",
                price: 12.99,
                quantity: 1
            },
            {
                mealId: '4',
                name: "Authentic Pad Thai",
                price: 11.99,
                quantity: 1
            }
        ],
        status: 'pending',
        total: 24.98,
        deliveryAddress: "789 Pine St, Elsewhere, USA",
        deliveryFee: 3.99,
        placedAt: "2025-06-05T13:00:00Z",
        estimatedDelivery: "2025-06-05T14:00:00Z"
    },
    {
        id: 'co2',
        userId: 'u7',
        cookId: '1',
        items: [
            {
                mealId: '1',
                name: "Homemade Lasagna",
                price: 12.99,
                quantity: 2
            }
        ],
        status: 'confirmed',
        total: 25.98,
        deliveryAddress: "101 Maple Dr, Nowhere, USA",
        deliveryFee: 3.99,
        placedAt: "2025-06-05T13:30:00Z",
        estimatedDelivery: "2025-06-05T14:30:00Z"
    }
];