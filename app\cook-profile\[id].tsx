import React from 'react';
import { StyleSheet, Text, View, Image } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { MapPin } from 'lucide-react-native';
import Colors from '@/constants/colors';
import Button from '@/components/Button';

// Mock user data
const mockUsers = [
    {
        id: 'u1',
        name: '<PERSON>',
        city: 'New York',
        profileImage: 'https://images.unsplash.com/photo-1583394838336-acd977736f90?ixlib=rb-1.2.1&auto=format&fit=crop&w=684&q=80',
    },
    {
        id: 'u2',
        name: '<PERSON>',
        city: 'Chicago',
        profileImage: 'https://images.unsplash.com/photo-1566554273541-37a9ca77b91f?ixlib=rb-1.2.1&auto=format&fit=crop&w=634&q=80',
    },
    {
        id: 'u3',
        name: '<PERSON>',
        city: 'Los Angeles',
        profileImage: 'https://images.unsplash.com/photo-1581299894007-aaa50297cf16?ixlib=rb-1.2.1&auto=format&fit=crop&w=634&q=80',
    },
    {
        id: 'u4',
        name: 'John Doe',
        city: 'Boston',
        profileImage: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    }
];

export default function UserProfileScreen() {
    const { id } = useLocalSearchParams<{ id: string }>();
    const router = useRouter();

    const user = mockUsers.find(u => u.id === id);

    if (!user) {
        return (
            <View style={styles.errorContainer}>
                <Text style={styles.errorText}>User not found</Text>
                <Button title="Go Back" onPress={() => router.back()} variant="outline" />
            </View>
        );
    }

    return (
        <View style={styles.container}>
            <View style={styles.profileContainer}>
                <Image source={{ uri: user.profileImage }} style={styles.profileImage} />
                <Text style={styles.name}>{user.name}</Text>

                <View style={styles.locationContainer}>
                    <MapPin size={16} color={Colors.textLight} />
                    <Text