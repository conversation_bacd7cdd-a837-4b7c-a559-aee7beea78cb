import React from 'react';
import { StyleSheet, Text, View, ScrollView, Image } from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import { MapPin, Clock, CreditCard } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { orders } from '@/mocks/orders';
import { cooks } from '@/mocks/cooks';
import { meals } from '@/mocks/meals';
import Button from '@/components/Button';

export default function OrderDetailScreen() {
    const { id } = useLocalSearchParams<{ id: string }>();

    const order = orders.find(o => o.id === id);

    if (!order) {
        return (
            <View style={styles.errorContainer}>
                <Text style={styles.errorText}>Order not found</Text>
            </View>
        );
    }

    const cook = cooks.find(c => c.id === order.cookId);

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'pending':
                return Colors.warning;
            case 'confirmed':
            case 'preparing':
            case 'ready':
                return Colors.primary;
            case 'out-for-delivery':
                return Colors.secondary;
            case 'delivered':
                return Colors.success;
            case 'cancelled':
                return Colors.error;
            default:
                return Colors.textLight;
        }
    };

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleString('en-US', {
            month: 'short',
            day: 'numeric',
            hour: 'numeric',
            minute: '2-digit'
        });
    };

    const formatStatus = (status: string) => {
        return status.split('-').map(word =>
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
    };

    const getMealImage = (mealId: string) => {
        const meal = meals.find(m => m.id === mealId);
        return meal ? meal.image : '';
    };

    return (
        <ScrollView style={styles.container}>
            <View style={styles.header}>
                <Text style={styles.orderId}>Order #{order.id.slice(-4)}</Text>
                <View style={[styles.statusBadge, { backgroundColor: getStatusColor(order.status) }]}>
                    <Text style={styles.statusText}>{formatStatus(order.status)}</Text>
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Order Details</Text>

                <View style={styles.infoRow}>
                    <Clock size={20} color={Colors.textLight} style={styles.infoIcon} />
                    <View>
                        <Text style={styles.infoLabel}>Order Placed</Text>
                        <Text style={styles.infoValue}>{formatDate(order.placedAt)}</Text>
                    </View>
                </View>

                <View style={styles.infoRow}>
                    <Clock size={20} color={Colors.textLight} style={styles.infoIcon} />
                    <View>
                        <Text style={styles.infoLabel}>Estimated Delivery</Text>
                        <Text style={styles.infoValue}>{formatDate(order.estimatedDelivery)}</Text>
                    </View>
                </View>

                <View style={styles.infoRow}>
                    <MapPin size={20} color={Colors.textLight} style={styles.infoIcon} />
                    <View>
                        <Text style={styles.infoLabel}>Delivery Address</Text>
                        <Text style={styles.infoValue}>{order.deliveryAddress}</Text>
                    </View>
                </View>

                <View style={styles.infoRow}>
                    <CreditCard size={20} color={Colors.textLight} style={styles.infoIcon} />
                    <View>
                        <Text style={styles.infoLabel}>Payment Method</Text>
                        <Text style={styles.infoValue}>Credit Card</Text>
                    </View>
                </View>
            </View>

            {cook && (
                <View style={styles.section}>
                    <Text style={styles.sectionTitle}>Cook Information</Text>

                    <View style={styles.cookInfo}>
                        <Image source={{ uri: cook.profileImage }} style={styles.cookImage} />
                        <View style={styles.cookDetails}>
                            <Text style={styles.cookName}>{cook.name}</Text>
                            <Text style={styles.cookRating}>★ {cook.rating.toFixed(1)} ({cook.reviewCount} reviews)</Text>
                        </View>
                    </View>
                </View>
            )}

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Order Items</Text>

                {order.items.map((item, index) => (
                    <View key={index} style={styles.orderItem}>
                        <Image
                            source={{ uri: getMealImage(item.mealId) }}
                            style={styles.itemImage}
                        />
                        <View style={styles.itemDetails}>
                            <Text style={styles.itemName}>{item.name}</Text>
                            <Text style={styles.itemQuantity}>Quantity: {item.quantity}</Text>
                            <Text style={styles.itemPrice}>${item.price.toFixed(2)}</Text>
                        </View>
                    </View>
                ))}
            </View>

            {order.specialInstructions && (
                <View style={styles.section}>
                    <Text style={styles.sectionTitle}>Special Instructions</Text>
                    <Text style={styles.instructions}>{order.specialInstructions}</Text>
                </View>
            )}

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Order Summary</Text>

                <View style={styles.summaryRow}>
                    <Text style={styles.summaryLabel}>Subtotal</Text>
                    <Text style={styles.summaryValue}>
                        ${(order.total - order.deliveryFee).toFixed(2)}
                    </Text>
                </View>

                <View style={styles.summaryRow}>
                    <Text style={styles.summaryLabel}>Delivery Fee</Text>
                    <Text style={styles.summaryValue}>${order.deliveryFee.toFixed(2)}</Text>
                </View>

                <View style={[styles.summaryRow, styles.totalRow]}>
                    <Text style={styles.totalLabel}>Total</Text>
                    <Text style={styles.totalValue}>${order.total.toFixed(2)}</Text>
                </View>
            </View>

            {order.status === 'delivered' && (
                <View style={styles.footer}>
                    <Button
                        title="Rate this Order"
                        onPress={() => { }}
                        variant="outline"
                        fullWidth
                    />
                    <Button
                        title="Reorder"
                        onPress={() => { }}
                        fullWidth
                        style={styles.reorderButton}
                    />
                </View>
            )}
        </ScrollView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    errorContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        padding: 24,
    },
    errorText: {
        fontSize: 18,
        color: Colors.text,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: Colors.border,
    },
    orderId: {
        fontSize: 20,
        fontWeight: 'bold',
        color: Colors.text,
    },
    statusBadge: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
    },
    statusText: {
        color: Colors.white,
        fontSize: 14,
        fontWeight: '500',
    },
    section: {
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: Colors.border,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: Colors.text,
        marginBottom: 16,
    },
    infoRow: {
        flexDirection: 'row',
        marginBottom: 16,
    },
    infoIcon: {
        marginRight: 12,
        marginTop: 2,
    },
    infoLabel: {
        fontSize: 14,
        color: Colors.textLight,
        marginBottom: 4,
    },
    infoValue: {
        fontSize: 16,
        color: Colors.text,
    },
    cookInfo: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    cookImage: {
        width: 50,
        height: 50,
        borderRadius: 25,
        marginRight: 16,
    },
    cookDetails: {
        flex: 1,
    },
    cookName: {
        fontSize: 16,
        fontWeight: '500',
        color: Colors.text,
        marginBottom: 4,
    },
    cookRating: {
        fontSize: 14,
        color: Colors.textLight,
    },
    orderItem: {
        flexDirection: 'row',
        marginBottom: 16,
    },
    itemImage: {
        width: 60,
        height: 60,
        borderRadius: 8,
    },
    itemDetails: {
        flex: 1,
        marginLeft: 12,
    },
    itemName: {
        fontSize: 16,
        fontWeight: '500',
        color: Colors.text,
        marginBottom: 4,
    },
    itemQuantity: {
        fontSize: 14,
        color: Colors.textLight,
        marginBottom: 4,
    },
    itemPrice: {
        fontSize: 16,
        fontWeight: '600',
        color: Colors.primary,
    },
    instructions: {
        fontSize: 16,
        color: Colors.text,
        lineHeight: 24,
    },
    summaryRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
    summaryLabel: {
        fontSize: 14,
        color: Colors.textLight,
    },
    summaryValue: {
        fontSize: 14,
        color: Colors.text,
    },
    totalRow: {
        marginTop: 8,
        paddingTop: 8,
        borderTopWidth: 1,
        borderTopColor: Colors.border,
    },
    totalLabel: {
        fontSize: 16,
        fontWeight: '600',
        color: Colors.text,
    },
    totalValue: {
        fontSize: 18,
        fontWeight: 'bold',
        color: Colors.primary,
    },
    footer: {
        padding: 16,
        gap: 12,
    },
    reorderButton: {
        marginTop: 8,
    },
});