import Colors from '@/constants/Colors';
import { useAuthStore } from '@/store/authStore';
import { Tabs } from 'expo-router';
import { ChefHat, Clock, Home, ShoppingBag, User } from 'lucide-react-native';
import React from 'react';

export default function TabLayout() {
  const user = useAuthStore(state => state.user);
  const isCook = user?.isCook;

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors.primary,
        tabBarInactiveTintColor: Colors.textLight,
        tabBarStyle: {
          borderTopColor: Colors.border,
        },
        headerStyle: {
          backgroundColor: Colors.background,
        },
        headerTitleStyle: {
          color: Colors.text,
          fontWeight: '600',
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ color }) => <Home size={24} color={color} />,
        }}
      />

      {isCook ? (
        <Tabs.Screen
          name="cook-meals"
          options={{
            title: 'My Meals',
            tabBarIcon: ({ color }) => <ChefHat size={24} color={color} />,
          }}
        />
      ) : (
        <Tabs.Screen
          name="explore"
          options={{
            title: 'Explore',
            tabBarIcon: ({ color }) => <ShoppingBag size={24} color={color} />,
          }}
        />
      )}

      <Tabs.Screen
        name="orders"
        options={{
          title: 'Orders',
          tabBarIcon: ({ color }) => <Clock size={24} color={color} />,
        }}
      />

      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color }) => <User size={24} color={color} />,
        }}
      />
    </Tabs>
  );
}