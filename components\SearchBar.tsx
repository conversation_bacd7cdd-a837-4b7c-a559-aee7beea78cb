import Colors from '@/constants/Colors';
import { Search, X } from 'lucide-react-native';
import React from 'react';
import { Pressable, StyleSheet, TextInput, View } from 'react-native';

interface SearchBarProps {
    value: string;
    onChangeText: (text: string) => void;
    placeholder?: string;
    onSubmit?: () => void;
}

export default function SearchBar({
    value,
    onChangeText,
    placeholder = "Search for meals...",
    onSubmit
}: SearchBarProps) {
    return (
        <View style={styles.container}>
            <Search size={20} color={Colors.textLight} style={styles.icon} />
            <TextInput
                style={styles.input}
                value={value}
                onChangeText={onChangeText}
                placeholder={placeholder}
                placeholderTextColor={Colors.textLight}
                returnKeyType="search"
                onSubmitEditing={onSubmit}
            />
            {value.length > 0 && (
                <Pressable onPress={() => onChangeText("")} style={styles.clearButton}>
                    <X size={18} color={Colors.textLight} />
                </Pressable>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.card,
        borderRadius: 8,
        paddingHorizontal: 12,
        marginHorizontal: 16,
        marginVertical: 8,
        height: 48,
    },
    icon: {
        marginRight: 8,
    },
    input: {
        flex: 1,
        fontSize: 16,
        color: Colors.text,
        height: '100%',
    },
    clearButton: {
        padding: 4,
    },
});