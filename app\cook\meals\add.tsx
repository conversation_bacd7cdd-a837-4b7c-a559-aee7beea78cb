import React, { useState } from 'react';
import { StyleSheet, Text, View, TextInput, ScrollView, Pressable, Image, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { Plus, X, Camera } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import Colors from '@/constants/colors';
import Button from '@/components/Button';

export default function AddMealScreen() {
    const router = useRouter();

    const [name, setName] = useState('');
    const [description, setDescription] = useState('');
    const [price, setPrice] = useState('');
    const [category, setCategory] = useState('');
    const [preparationTime, setPreparationTime] = useState('');
    const [availablePortions, setAvailablePortions] = useState('');
    const [ingredients, setIngredients] = useState<string[]>([]);
    const [allergens, setAllergens] = useState<string[]>([]);
    const [image, setImage] = useState('');
    const [newIngredient, setNewIngredient] = useState('');
    const [newAllergen, setNewAllergen] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const handleAddIngredient = () => {
        if (newIngredient.trim()) {
            setIngredients([...ingredients, newIngredient.trim()]);
            setNewIngredient('');
        }
    };

    const handleRemoveIngredient = (index: number) => {
        setIngredients(ingredients.filter((_, i) => i !== index));
    };

    const handleAddAllergen = () => {
        if (newAllergen.trim()) {
            setAllergens([...allergens, newAllergen.trim()]);
            setNewAllergen('');
        }
    };

    const handleRemoveAllergen = (index: number) => {
        setAllergens(allergens.filter((_, i) => i !== index));
    };

    const handlePickImage = async () => {
        const result = await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ImagePicker.MediaTypeOptions.Images,
            allowsEditing: true,
            aspect: [16, 9],
            quality: 0.8,
        });

        if (!result.canceled) {
            setImage(result.assets[0].uri);
        }
    };

    const validateForm = () => {
        if (!name.trim()) {
            Alert.alert('Error', 'Please enter a meal name');
            return false;
        }

        if (!description.trim()) {
            Alert.alert('Error', 'Please enter a description');
            return false;
        }

        if (!price.trim() || isNaN(Number(price))) {
            Alert.alert('Error', 'Please enter a valid price');
            return false;
        }

        if (!category.trim()) {
            Alert.alert('Error', 'Please enter a category');
            return false;
        }

        if (!preparationTime.trim() || isNaN(Number(preparationTime))) {
            Alert.alert('Error', 'Please enter a valid preparation time');
            return false;
        }

        if (!availablePortions.trim() || isNaN(Number(availablePortions))) {
            Alert.alert('Error', 'Please enter a valid number of available portions');
            return false;
        }

        if (ingredients.length === 0) {
            Alert.alert('Error', 'Please add at least one ingredient');
            return false;
        }

        if (!image) {
            Alert.alert('Error', 'Please add a meal image');
            return false;
        }

        return true;
    };

    const handleSubmit = () => {
        if (!validateForm()) return;

        setIsLoading(true);

        // Simulate API call
        setTimeout(() => {
            setIsLoading(false);

            Alert.alert(
                'Success',
                'Meal added successfully',
                [
                    {
                        text: 'OK',
                        onPress: () => router.push('/(tabs)/cook-meals')
                    }
                ]
            );
        }, 1500);
    };

    return (
        <ScrollView style={styles.container}>
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Meal Information</Text>

                <View style={styles.imageContainer}>
                    {image ? (
                        <Image source={{ uri: image }} style={styles.mealImage} />
                    ) : (
                        <Pressable style={styles.imagePlaceholder} onPress={handlePickImage}>
                            <Camera size={32} color={Colors.textLight} />
                            <Text style={styles.imagePlaceholderText}>Add Meal Image</Text>
                        </Pressable>
                    )}

                    <Button
                        title={image ? "Change Image" : "Select Image"}
                        onPress={handlePickImage}
                        variant="outline"
                        size="small"
                        style={styles.imageButton}
                    />
                </View>

                <View style={styles.inputContainer}>
                    <Text style={styles.label}>Meal Name</Text>
                    <TextInput
                        style={styles.input}
                        placeholder="Enter meal name"
                        value={name}
                        onChangeText={setName}
                    />
                </View>

                <View style={styles.inputContainer}>
                    <Text style={styles.label}>Description</Text>
                    <TextInput
                        style={[styles.input, styles.textArea]}
                        placeholder="Describe your meal"
                        value={description}
                        onChangeText={setDescription}
                        multiline
                        numberOfLines={4}
                    />
                </View>

                <View style={styles.row}>
                    <View style={[styles.inputContainer, { flex: 1, marginRight: 8 }]}>
                        <Text style={styles.label}>Price ($)</Text>
                        <TextInput
                            style={styles.input}
                            placeholder="0.00"
                            value={price}
                            onChangeText={setPrice}
                            keyboardType="decimal-pad"
                        />
                    </View>

                    <View style={[styles.inputContainer, { flex: 1, marginLeft: 8 }]}>
                        <Text style={styles.label}>Category</Text>
                        <TextInput
                            style={styles.input}
                            placeholder="e.g. Italian"
                            value={category}
                            onChangeText={setCategory}
                        />
                    </View>
                </View>

                <View style={styles.row}>
                    <View style={[styles.inputContainer, { flex: 1, marginRight: 8 }]}>
                        <Text style={styles.label}>Prep Time (min)</Text>
                        <TextInput
                            style={styles.input}
                            placeholder="30"
                            value={preparationTime}
                            onChangeText={setPreparationTime}
                            keyboardType="number-pad"
                        />
                    </View>

                    <View style={[styles.inputContainer, { flex: 1, marginLeft: 8 }]}>
                        <Text style={styles.label}>Available Portions</Text>
                        <TextInput
                            style={styles.input}
                            placeholder="10"
                            value={availablePortions}
                            onChangeText={setAvailablePortions}
                            keyboardType="number-pad"
                        />
                    </View>
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Ingredients</Text>

                <View style={styles.listContainer}>
                    {ingredients.map((ingredient, index) => (
                        <View key={index} style={styles.listItem}>
                            <Text style={styles.listItemText}>{ingredient}</Text>
                            <Pressable
                                style={styles.removeButton}
                                onPress={() => handleRemoveIngredient(index)}
                            >
                                <X size={16} color={Colors.error} />
                            </Pressable>
                        </View>
                    ))}

                    <View style={styles.addItemContainer}>
                        <TextInput
                            style={styles.addItemInput}
                            placeholder="Add ingredient"
                            value={newIngredient}
                            onChangeText={setNewIngredient}
                            onSubmitEditing={handleAddIngredient}
                        />
                        <Pressable
                            style={styles.addButton}
                            onPress={handleAddIngredient}
                        >
                            <Plus size={20} color={Colors.white} />
                        </Pressable>
                    </View>
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Allergens</Text>

                <View style={styles.listContainer}>
                    {allergens.map((allergen, index) => (
                        <View key={index} style={styles.listItem}>
                            <Text style={styles.listItemText}>{allergen}</Text>
                            <Pressable
                                style={styles.removeButton}
                                onPress={() => handleRemoveAllergen(index)}
                            >
                                <X size={16} color={Colors.error} />
                            </Pressable>
                        </View>
                    ))}

                    <View style={styles.addItemContainer}>
                        <TextInput
                            style={styles.addItemInput}
                            placeholder="Add allergen (or 'None' if none)"
                            value={newAllergen}
                            onChangeText={setNewAllergen}
                            onSubmitEditing={handleAddAllergen}
                        />
                        <Pressable
                            style={styles.addButton}
                            onPress={handleAddAllergen}
                        >
                            <Plus size={20} color={Colors.white} />
                        </Pressable>
                    </View>
                </View>
            </View>

            <View style={styles.footer}>
                <Button
                    title="Cancel"
                    onPress={() => router.back()}
                    variant="outline"
                    style={styles.cancelButton}
                />
                <Button
                    title="Add Meal"
                    onPress={handleSubmit}
                    loading={isLoading}
                    style={styles.submitButton}
                />
            </View>
        </ScrollView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    section: {
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: Colors.border,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: Colors.text,
        marginBottom: 16,
    },
    imageContainer: {
        alignItems: 'center',
        marginBottom: 16,
    },
    mealImage: {
        width: '100%',
        height: 200,
        borderRadius: 8,
        marginBottom: 8,
    },
    imagePlaceholder: {
        width: '100%',
        height: 200,
        borderRadius: 8,
        backgroundColor: Colors.card,
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 8,
        borderWidth: 1,
        borderColor: Colors.border,
        borderStyle: 'dashed',
    },
    imagePlaceholderText: {
        fontSize: 16,
        color: Colors.textLight,
        marginTop: 8,
    },
    imageButton: {
        width: 150,
    },
    inputContainer: {
        marginBottom: 16,
    },
    label: {
        fontSize: 14,
        fontWeight: '500',
        color: Colors.text,
        marginBottom: 8,
    },
    input: {
        backgroundColor: Colors.white,
        borderWidth: 1,
        borderColor: Colors.border,
        borderRadius: 8,
        padding: 12,
        fontSize: 16,
        color: Colors.text,
    },
    textArea: {
        height: 100,
        textAlignVertical: 'top',
    },
    row: {
        flexDirection: 'row',
    },
    listContainer: {
        marginBottom: 8,
    },
    listItem: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.white,
        borderWidth: 1,
        borderColor: Colors.border,
        borderRadius: 8,
        padding: 12,
        marginBottom: 8,
    },
    listItemText: {
        flex: 1,
        fontSize: 16,
        color: Colors.text,
    },
    removeButton: {
        padding: 4,
    },
    addItemContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    addItemInput: {
        flex: 1,
        backgroundColor: Colors.white,
        borderWidth: 1,
        borderColor: Colors.border,
        borderRadius: 8,
        padding: 12,
        fontSize: 16,
        color: Colors.text,
        marginRight: 8,
    },
    addButton: {
        backgroundColor: Colors.primary,
        borderRadius: 8,
        padding: 12,
        justifyContent: 'center',
        alignItems: 'center',
    },
    footer: {
        flexDirection: 'row',
        padding: 16,
        marginBottom: 24,
    },
    cancelButton: {
        flex: 1,
        marginRight: 8,
    },
    submitButton: {
        flex: 1,
        marginLeft: 8,
    },
});