import Button from '@/components/Button';
import Colors from '@/constants/Colors';
import { meals } from '@/mocks/meals';
import { cookOrders } from '@/mocks/orders';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Clock, CreditCard, MapPin } from 'lucide-react-native';
import React, { useState } from 'react';
import { Alert, Image, ScrollView, StyleSheet, Text, View } from 'react-native';

export default function CookOrderDetailScreen() {
    const { id } = useLocalSearchParams<{ id: string }>();
    const router = useRouter();

    const [order, setOrder] = useState(cookOrders.find(o => o.id === id));
    const [isLoading, setIsLoading] = useState(false);

    if (!order) {
        return (
            <View style={styles.errorContainer}>
                <Text style={styles.errorText}>Order not found</Text>
            </View>
        );
    }

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleString('en-US', {
            month: 'short',
            day: 'numeric',
            hour: 'numeric',
            minute: '2-digit'
        });
    };

    const formatStatus = (status: string) => {
        return status.split('-').map(word =>
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
    };

    const getMealImage = (mealId: string) => {
        const meal = meals.find(m => m.id === mealId);
        return meal ? meal.image : '';
    };

    const handleUpdateStatus = (newStatus: string) => {
        setIsLoading(true);

        // Simulate API call
        setTimeout(() => {
            setOrder(prev => prev ? { ...prev, status: newStatus } : null);
            setIsLoading(false);

            Alert.alert(
                'Status Updated',
                `Order status updated to ${formatStatus(newStatus)}`
            );
        }, 1000);
    };

    const getNextStatusButton = () => {
        switch (order.status) {
            case 'pending':
                return (
                    <Button
                        title="Confirm Order"
                        onPress={() => handleUpdateStatus('confirmed')}
                        loading={isLoading}
                        fullWidth
                    />
                );
            case 'confirmed':
                return (
                    <Button
                        title="Start Preparing"
                        onPress={() => handleUpdateStatus('preparing')}
                        loading={isLoading}
                        fullWidth
                    />
                );
            case 'preparing':
                return (
                    <Button
                        title="Mark as Ready"
                        onPress={() => handleUpdateStatus('ready')}
                        loading={isLoading}
                        fullWidth
                    />
                );
            case 'ready':
                return (
                    <Button
                        title="Out for Delivery"
                        onPress={() => handleUpdateStatus('out-for-delivery')}
                        loading={isLoading}
                        fullWidth
                    />
                );
            default:
                return null;
        }
    };

    const getCancelButton = () => {
        if (['pending', 'confirmed'].includes(order.status)) {
            return (
                <Button
                    title="Cancel Order"
                    onPress={() => handleUpdateStatus('cancelled')}
                    variant="outline"
                    loading={isLoading}
                    textStyle={{ color: Colors.error }}
                    style={{ borderColor: Colors.error, marginTop: 12 }}
                    fullWidth
                />
            );
        }
        return null;
    };

    return (
        <ScrollView style={styles.container}>
            <View style={styles.header}>
                <Text style={styles.orderId}>Order #{order.id.slice(-4)}</Text>
                <View style={[
                    styles.statusBadge,
                    { backgroundColor: getStatusColor(order.status) }
                ]}>
                    <Text style={styles.statusText}>{formatStatus(order.status)}</Text>
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Order Details</Text>

                <View style={styles.infoRow}>
                    <Clock size={20} color={Colors.textLight} style={styles.infoIcon} />
                    <View>
                        <Text style={styles.infoLabel}>Order Placed</Text>
                        <Text style={styles.infoValue}>{formatDate(order.placedAt)}</Text>
                    </View>
                </View>

                <View style={styles.infoRow}>
                    <Clock size={20} color={Colors.textLight} style={styles.infoIcon} />
                    <View>
                        <Text style={styles.infoLabel}>Estimated Delivery</Text>
                        <Text style={styles.infoValue}>{formatDate(order.estimatedDelivery)}</Text>
                    </View>
                </View>

                <View style={styles.infoRow}>
                    <MapPin size={20} color={Colors.textLight} style={styles.infoIcon} />
                    <View>
                        <Text style={styles.infoLabel}>Delivery Address</Text>
                        <Text style={styles.infoValue}>{order.deliveryAddress}</Text>
                    </View>
                </View>

                <View style={styles.infoRow}>
                    <CreditCard size={20} color={Colors.textLight} style={styles.infoIcon} />
                    <View>
                        <Text style={styles.infoLabel}>Payment Method</Text>
                        <Text style={styles.infoValue}>Credit Card</Text>
                    </View>
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Customer Information</Text>
                <Text style={styles.customerName}>John Doe</Text>
                <Text style={styles.customerPhone}>************</Text>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Order Items</Text>

                {order.items.map((item, index) => (
                    <View key={index} style={styles.orderItem}>
                        <Image
                            source={{ uri: getMealImage(item.mealId) }}
                            style={styles.itemImage}
                        />
                        <View style={styles.itemDetails}>
                            <Text style={styles.itemName}>{item.name}</Text>
                            <Text style={styles.itemQuantity}>Quantity: {item.quantity}</Text>
                            <Text style={styles.itemPrice}>${item.price.toFixed(2)}</Text>
                        </View>
                    </View>
                ))}
            </View>

            {order.specialInstructions && (
                <View style={styles.section}>
                    <Text style={styles.sectionTitle}>Special Instructions</Text>
                    <Text style={styles.instructions}>{order.specialInstructions}</Text>
                </View>
            )}

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Order Summary</Text>

                <View style={styles.summaryRow}>
                    <Text style={styles.summaryLabel}>Subtotal</Text>
                    <Text style={styles.summaryValue}>
                        ${(order.total - order.deliveryFee).toFixed(2)}
                    </Text>
                </View>

                <View style={styles.summaryRow}>
                    <Text style={styles.summaryLabel}>Delivery Fee</Text>
                    <Text style={styles.summaryValue}>${order.deliveryFee.toFixed(2)}</Text>
                </View>

                <View style={[styles.summaryRow, styles.totalRow]}>
                    <Text style={styles.totalLabel}>Total</Text>
                    <Text style={styles.totalValue}>${order.total.toFixed(2)}</Text>
                </View>
            </View>

            <View style={styles.footer}>
                {getNextStatusButton()}
                {getCancelButton()}
            </View>
        </ScrollView>
    );
}

function getStatusColor(status: string) {
    switch (status) {
        case 'pending':
            return Colors.warning;
        case 'confirmed':
        case 'preparing':
        case 'ready':
            return Colors.primary;
        case 'out-for-delivery':
            return Colors.secondary;
        case 'delivered':
            return Colors.success;
        case 'cancelled':
            return Colors.error;
        default:
            return Colors.textLight;
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    errorContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        padding: 24,
    },
    errorText: {
        fontSize: 18,
        color: Colors.text,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: Colors.border,
    },
    orderId: {
        fontSize: 20,
        fontWeight: 'bold',
        color: Colors.text,
    },
    statusBadge: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
    },
    statusText: {
        color: Colors.white,
        fontSize: 14,
        fontWeight: '500',
    },
    section: {
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: Colors.border,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: Colors.text,
        marginBottom: 16,
    },
    infoRow: {
        flexDirection: 'row',
        marginBottom: 16,
    },
    infoIcon: {
        marginRight: 12,
        marginTop: 2,
    },
    infoLabel: {
        fontSize: 14,
        color: Colors.textLight,
        marginBottom: 4,
    },
    infoValue: {
        fontSize: 16,
        color: Colors.text,
    },
    customerName: {
        fontSize: 16,
        fontWeight: '500',
        color: Colors.text,
        marginBottom: 4,
    },
    customerPhone: {
        fontSize: 14,
        color: Colors.textLight,
    },
    orderItem: {
        flexDirection: 'row',
        marginBottom: 16,
    },
    itemImage: {
        width: 60,
        height: 60,
        borderRadius: 8,
    },
    itemDetails: {
        flex: 1,
        marginLeft: 12,
    },
    itemName: {
        fontSize: 16,
        fontWeight: '500',
        color: Colors.text,
        marginBottom: 4,
    },
    itemQuantity: {
        fontSize: 14,
        color: Colors.textLight,
        marginBottom: 4,
    },
    itemPrice: {
        fontSize: 16,
        fontWeight: '600',
        color: Colors.primary,
    },
    instructions: {
        fontSize: 16,
        color: Colors.text,
        lineHeight: 24,
    },
    summaryRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
    summaryLabel: {
        fontSize: 14,
        color: Colors.textLight,
    },
    summaryValue: {
        fontSize: 14,
        color: Colors.text,
    },
    totalRow: {
        marginTop: 8,
        paddingTop: 8,
        borderTopWidth: 1,
        borderTopColor: Colors.border,
    },
    totalLabel: {
        fontSize: 16,
        fontWeight: '600',
        color: Colors.text,
    },
    totalValue: {
        fontSize: 18,
        fontWeight: 'bold',
        color: Colors.primary,
    },
    footer: {
        padding: 16,
        marginBottom: 24,
    },
});