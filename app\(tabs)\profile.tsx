import React from 'react';
import { StyleSheet, Text, View, Image, ScrollView, Pressable, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { LogOut, ChevronRight, Settings, CreditCard, MapPin, Bell, HelpCircle, UserCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useAuthStore } from '@/store/authStore';
import Button from '@/components/Button';

export default function ProfileScreen() {
    const router = useRouter();
    const user = useAuthStore(state => state.user);
    const logout = useAuthStore(state => state.logout);
    const toggleUserType = useAuthStore(state => state.toggleUserType);

    const isCook = user?.isCook;

    const handleLogout = () => {
        Alert.alert(
            'Logout',
            'Are you sure you want to logout?',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Logout',
                    style: 'destructive',
                    onPress: () => {
                        logout();
                        router.replace('/(auth)');
                    }
                }
            ]
        );
    };

    const handleToggleUserType = () => {
        Alert.alert(
            'Switch Account Type',
            `Switch to ${isCook ? 'Customer' : 'Cook'} account?`,
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Switch',
                    onPress: () => {
                        toggleUserType();
                    }
                }
            ]
        );
    };

    const menuItems = [
        {
            icon: <UserCircle size={24} color={Colors.text} />,
            title: 'Personal Information',
            onPress: () => { }
        },
        {
            icon: <MapPin size={24} color={Colors.text} />,
            title: 'Saved Addresses',
            onPress: () => { }
        },
        {
            icon: <CreditCard size={24} color={Colors.text} />,
            title: 'Payment Methods',
            onPress: () => { }
        },
        {
            icon: <Bell size={24} color={Colors.text} />,
            title: 'Notifications',
            onPress: () => { }
        },
        {
            icon: <Settings size={24} color={Colors.text} />,
            title: 'Settings',
            onPress: () => { }
        },
        {
            icon: <HelpCircle size={24} color={Colors.text} />,
            title: 'Help & Support',
            onPress: () => { }
        }
    ];

    return (
        <ScrollView style={styles.container}>
            <View style={styles.header}>
                <Image
                    source={{
                        uri: isCook
                            ? 'https://images.unsplash.com/photo-*************-acd977736f90?ixlib=rb-1.2.1&auto=format&fit=crop&w=684&q=80'
                            : 'https://images.unsplash.com/photo-*************-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
                    }}
                    style={styles.profileImage}
                />
                <Text style={styles.name}>{user?.name}</Text>
                <Text style={styles.email}>{user?.email}</Text>

                <View style={styles.accountTypeContainer}>
                    <Text style={styles.accountType}>
                        {isCook ? 'Cook Account' : 'Customer Account'}
                    </Text>
                    <Button
                        title={`Switch to ${isCook ? 'Customer' : 'Cook'}`}
                        onPress={handleToggleUserType}
                        variant="outline"
                        size="small"
                        style={styles.switchButton}
                    />
                </View>
            </View>

            <View style={styles.menuContainer}>
                {menuItems.map((item, index) => (
                    <Pressable
                        key={index}
                        style={styles.menuItem}
                        onPress={item.onPress}
                    >
                        <View style={styles.menuItemLeft}>
                            {item.icon}
                            <Text style={styles.menuItemText}>{item.title}</Text>
                        </View>
                        <ChevronRight size={20} color={Colors.textLight} />
                    </Pressable>
                ))}
            </View>

            <Button
                title="Logout"
                onPress={handleLogout}
                variant="outline"
                style={styles.logoutButton}
                textStyle={{ color: Colors.error }}
                fullWidth
            />

            <Text style={styles.version}>Version 1.0.0</Text>
        </ScrollView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    header: {
        alignItems: 'center',
        padding: 24,
        borderBottomWidth: 1,
        borderBottomColor: Colors.border,
    },
    profileImage: {
        width: 100,
        height: 100,
        borderRadius: 50,
        marginBottom: 16,
    },
    name: {
        fontSize: 24,
        fontWeight: 'bold',
        color: Colors.text,
        marginBottom: 4,
    },
    email: {
        fontSize: 16,
        color: Colors.textLight,
        marginBottom: 16,
    },
    accountTypeContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 8,
    },
    accountType: {
        fontSize: 14,
        color: Colors.textLight,
        marginRight: 12,
    },
    switchButton: {
        borderColor: Colors.primary,
    },
    menuContainer: {
        marginTop: 16,
        paddingHorizontal: 16,
    },
    menuItem: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingVertical: 16,
        borderBottomWidth: 1,
        borderBottomColor: Colors.border,
    },
    menuItemLeft: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    menuItemText: {
        fontSize: 16,
        color: Colors.text,
        marginLeft: 16,
    },
    logoutButton: {
        marginHorizontal: 16,
        marginTop: 24,
        borderColor: Colors.error,
    },
    version: {
        textAlign: 'center',
        marginTop: 24,
        marginBottom: 32,
        fontSize: 14,
        color: Colors.textLight,
    },
});