import React from 'react';
import { StyleSheet, Text, View, Image, ScrollView, FlatList } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { MapPin, Star } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { cooks } from '@/mocks/cooks';
import { meals } from '@/mocks/meals';
import MealCard from '@/components/MealCard';
import Button from '@/components/Button';

export default function CookProfileScreen() {
    const { id } = useLocalSearchParams<{ id: string }>();
    const router = useRouter();

    const cook = cooks.find(c => c.id === id);
    const cookMeals = meals.filter(meal => meal.cookId === id);

    if (!cook) {
        return (
            <View style={styles.errorContainer}>
                <Text style={styles.errorText}>Cook not found</Text>
                <Button title="Go Back" onPress={() => router.back()} variant="outline" />
            </View>
        );
    }

    return (
        <ScrollView style={styles.container}>
            <View style={styles.header}>
                <Image source={{ uri: cook.profileImage }} style={styles.profileImage} />
                <Text style={styles.name}>{cook.name}</Text>

                <View style={styles.ratingContainer}>
                    <Star size={18} color={Colors.warning} fill={Colors.warning} />
                    <Text style={styles.rating}>{cook.rating.toFixed(1)}</Text>
                    <Text style={styles.reviews}>({cook.reviewCount} reviews)</Text>
                </View>

                <View style={styles.locationContainer}>
                    <MapPin size={16} color={Colors.textLight} />
                    <Text style={styles.location}>5 miles away</Text>
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>About</Text>
                <Text style={styles.bio}>{cook.bio}</Text>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Specialties</Text>
                <View style={styles.specialtiesContainer}>
                    {cook.specialties.map((specialty, index) => (
                        <View key={index} style={styles.specialtyTag}>
                            <Text style={styles.specialtyText}>{specialty}</Text>
                        </View>
                    ))}
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Menu</Text>
                {cookMeals.map(meal => (
                    <MealCard key={meal.id} meal={meal} />
                ))}
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Reviews</Text>
                <View style={styles.reviewCard}>
                    <View style={styles.reviewHeader}>
                        <Image
                            source={{ uri: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80' }}
                            style={styles.reviewerImage}
                        />
                        <View>
                            <Text style={styles.reviewerName}>John Doe</Text>
                            <View style={styles.reviewRating}>
                                <Star size={14} color={Colors.warning} fill={Colors.warning} />
                                <Star size={14} color={Colors.warning} fill={Colors.warning} />
                                <Star size={14} color={Colors.warning} fill={Colors.warning} />
                                <Star size={14} color={Colors.warning} fill={Colors.warning} />
                                <Star size={14} color={Colors.warning} fill={Colors.warning} />
                            </View>
                        </View>
                        <Text style={styles.reviewDate}>2 days ago</Text>
                    </View>
                    <Text style={styles.reviewText}>
                        Amazing food! The lasagna was delicious and tasted just like my grandmother used to make. Will definitely order again.
                    </Text>
                </View>

                <View style={styles.reviewCard}>
                    <View style={styles.reviewHeader}>
                        <Image
                            source={{ uri: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80' }}
                            style={styles.reviewerImage}
                        />
                        <View>
                            <Text style={styles.reviewerName}>Sarah Johnson</Text>
                            <View style={styles.reviewRating}>
                                <Star size={14} color={Colors.warning} fill={Colors.warning} />
                                <Star size={14} color={Colors.warning} fill={Colors.warning} />
                                <Star size={14} color={Colors.warning} fill={Colors.warning} />
                                <Star size={14} color={Colors.warning} fill={Colors.warning} />
                                <Star size={14} color={Colors.textLight} />
                            </View>
                        </View>
                        <Text style={styles.reviewDate}>1 week ago</Text>
                    </View>
                    <Text style={styles.reviewText}>
                        The food was great and arrived on time. Portions were generous and everything was still hot. Would recommend!
                    </Text>
                </View>

                <Button
                    title="See All Reviews"
                    variant="outline"
                    style={styles.seeAllButton}
                    onPress={() => { }}
                />
            </View>
        </ScrollView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,