import Button from '@/components/Button';
import Colors from '@/constants/Colors';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { MapPin } from 'lucide-react-native';
import React from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';

// Mock user data
const mockUsers = [
    {
        id: 'u1',
        name: '<PERSON>',
        city: 'New York',
        profileImage: 'https://images.unsplash.com/photo-1583394838336-acd977736f90?ixlib=rb-1.2.1&auto=format&fit=crop&w=684&q=80',
    },
    {
        id: 'u2',
        name: '<PERSON>',
        city: 'Chicago',
        profileImage: 'https://images.unsplash.com/photo-1566554273541-37a9ca77b91f?ixlib=rb-1.2.1&auto=format&fit=crop&w=634&q=80',
    },
    {
        id: 'u3',
        name: '<PERSON>',
        city: 'Los Angeles',
        profileImage: 'https://images.unsplash.com/photo-1581299894007-aaa50297cf16?ixlib=rb-1.2.1&auto=format&fit=crop&w=634&q=80',
    },
    {
        id: 'u4',
        name: 'John Doe',
        city: 'Boston',
        profileImage: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    }
];

export default function UserProfileScreen() {
    const { id } = useLocalSearchParams<{ id: string }>();
    const router = useRouter();

    const user = mockUsers.find(u => u.id === id);

    if (!user) {
        return (
            <View style={styles.errorContainer}>
                <Text style={styles.errorText}>User not found</Text>
                <Button title="Go Back" onPress={() => router.back()} variant="outline" />
            </View>
        );
    }

    return (
        <View style={styles.container}>
            <View style={styles.profileContainer}>
                <Image source={{ uri: user.profileImage }} style={styles.profileImage} />
                <Text style={styles.name}>{user.name}</Text>

                <View style={styles.locationContainer}>
                    <MapPin size={16} color={Colors.textLight} />
                    <Text style={styles.location}>{user.city}</Text>
                </View>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    errorText: {
        fontSize: 18,
        color: Colors.text,
        marginBottom: 20,
    },
    profileContainer: {
        alignItems: 'center',
        padding: 20,
        backgroundColor: Colors.white,
        margin: 16,
        borderRadius: 12,
        shadowColor: Colors.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    profileImage: {
        width: 120,
        height: 120,
        borderRadius: 60,
        marginBottom: 16,
    },
    name: {
        fontSize: 24,
        fontWeight: 'bold',
        color: Colors.text,
        marginBottom: 8,
    },
    locationContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 8,
    },
    location: {
        fontSize: 16,
        color: Colors.textLight,
        marginLeft: 4,
    },
});