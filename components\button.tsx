import React from 'react';
import { StyleSheet, Text, Pressable, ActivityIndicator, ViewStyle, TextStyle } from 'react-native';
import Colors from '@/constants/colors';

interface ButtonProps {
    title: string;
    onPress: () => void;
    variant?: 'primary' | 'secondary' | 'outline';
    size?: 'small' | 'medium' | 'large';
    disabled?: boolean;
    loading?: boolean;
    style?: ViewStyle;
    textStyle?: TextStyle;
    fullWidth?: boolean;
}

export default function Button({
    title,
    onPress,
    variant = 'primary',
    size = 'medium',
    disabled = false,
    loading = false,
    style,
    textStyle,
    fullWidth = false,
}: ButtonProps) {
    const getButtonStyle = () => {
        let buttonStyle: ViewStyle = {};

        // Variant styles
        switch (variant) {
            case 'primary':
                buttonStyle.backgroundColor = Colors.primary;
                break;
            case 'secondary':
                buttonStyle.backgroundColor = Colors.secondary;
                break;
            case 'outline':
                buttonStyle.backgroundColor = 'transparent';
                buttonStyle.borderWidth = 1;
                buttonStyle.borderColor = Colors.primary;
                break;
        }

        // Size styles
        switch (size) {
            case 'small':
                buttonStyle.paddingVertical = 8;
                buttonStyle.paddingHorizontal = 16;
                break;
            case 'medium':
                buttonStyle.paddingVertical = 12;
                buttonStyle.paddingHorizontal = 24;
                break;
            case 'large':
                buttonStyle.paddingVertical = 16;
                buttonStyle.paddingHorizontal = 32;
                break;
        }

        // Full width
        if (fullWidth) {
            buttonStyle.width = '100%';
        }

        // Disabled state
        if (disabled) {
            buttonStyle.opacity = 0.5;
        }

        return buttonStyle;
    };

    const getTextStyle = () => {
        let textStyleObj: TextStyle = {};

        switch (variant) {
            case 'primary':
            case 'secondary':
                textStyleObj.color = Colors.white;
                break;
            case 'outline':
                textStyleObj.color = Colors.primary;
                break;
        }

        switch (size) {
            case 'small':
                textStyleObj.fontSize = 14;
                break;
            case 'medium':
                textStyleObj.fontSize = 16;
                break;
            case 'large':
                textStyleObj.fontSize = 18;
                break;
        }

        return textStyleObj;
    };

    return (
        <Pressable
            style={({ pressed }) => [
                styles.button,
                getButtonStyle(),
                pressed && styles.pressed,
                style
            ]}
            onPress={onPress}
            disabled={disabled || loading}
        >
            {loading ? (
                <ActivityIndicator
                    color={variant === 'outline' ? Colors.primary : Colors.white}
                    size="small"
                />
            ) : (
                <Text style={[styles.text, getTextStyle(), textStyle]}>
                    {title}
                </Text>
            )}
        </Pressable>
    );
}

const styles = StyleSheet.create({
    button: {
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'row',
    },
    text: {
        fontWeight: '600',
        textAlign: 'center',
    },
    pressed: {
        opacity: 0.8,
    },
});