import React from 'react';
import { StyleSheet, Text, View, ScrollView, Pressable } from 'react-native';
import Colors from '@/constants/colors';

interface CategoryPillsProps {
    categories: string[];
    selectedCategory: string;
    onSelectCategory: (category: string) => void;
}

export default function CategoryPills({
    categories,
    selectedCategory,
    onSelectCategory
}: CategoryPillsProps) {
    return (
        <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.container}
        >
            {categories.map((category) => (
                <Pressable
                    key={category}
                    style={[
                        styles.pill,
                        selectedCategory === category && styles.selectedPill
                    ]}
                    onPress={() => onSelectCategory(category)}
                >
                    <Text
                        style={[
                            styles.pillText,
                            selectedCategory === category && styles.selectedPillText
                        ]}
                    >
                        {category}
                    </Text>
                </Pressable>
            ))}
        </ScrollView>
    );
}

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: 16,
        paddingVertical: 12,
        gap: 8,
    },
    pill: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        backgroundColor: Colors.card,
        marginRight: 8,
    },
    selectedPill: {
        backgroundColor: Colors.primary,
    },
    pillText: {
        fontSize: 14,
        color: Colors.text,
    },
    selectedPillText: {
        color: Colors.white,
        fontWeight: '500',
    },
});