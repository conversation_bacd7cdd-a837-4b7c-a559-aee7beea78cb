import React, { useState } from 'react';
import { StyleSheet, Text, View, TextInput, SafeAreaView, TouchableOpacity, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import Colors from '@/constants/colors';
import Button from '@/components/Button';

export default function RegisterScreen() {
    const router = useRouter();

    const [name, setName] = useState('');
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [isCook, setIsCook] = useState(false);

    const [nameError, setNameError] = useState('');
    const [emailError, setEmailError] = useState('');
    const [passwordError, setPasswordError] = useState('');
    const [confirmPasswordError, setConfirmPasswordError] = useState('');

    const validateForm = () => {
        let isValid = true;

        // Name validation
        if (!name.trim()) {
            setNameError('Name is required');
            isValid = false;
        } else {
            setNameError('');
        }

        // Email validation
        if (!email.trim()) {
            setEmailError('Email is required');
            isValid = false;
        } else if (!/\S+@\S+\.\S+/.test(email)) {
            setEmailError('Email is invalid');
            isValid = false;
        } else {
            setEmailError('');
        }

        // Password validation
        if (!password) {
            setPasswordError('Password is required');
            isValid = false;
        } else if (password.length < 6) {
            setPasswordError('Password must be at least 6 characters');
            isValid = false;
        } else {
            setPasswordError('');
        }

        // Confirm password validation
        if (password !== confirmPassword) {
            setConfirmPasswordError('Passwords do not match');
            isValid = false;
        } else {
            setConfirmPasswordError('');
        }

        return isValid;
    };

    const handleRegister = () => {
        if (!validateForm()) return;

        // In a real app, we would call an API to register the user
        Alert.alert(
            'Registration Successful',
            'Your account has been created. Please login.',
            [{ text: 'OK', onPress: () => router.push('/login') }]
        );
    };

    return (
        <SafeAreaView style={styles.container}>
            <TouchableOpacity
                style={styles.backButton}
                onPress={() => router.back()}
            >
                <ArrowLeft size={24} color={Colors.text} />
            </TouchableOpacity>

            <View style={styles.content}>
                <Text style={styles.title}>Create Account</Text>
                <Text style={styles.subtitle}>Sign up to get started</Text>

                <View style={styles.form}>
                    <View style={styles.inputContainer}>
                        <Text style={styles.label}>Full Name</Text>
                        <TextInput
                            style={[styles.input, nameError ? styles.inputError : null]}
                            placeholder="Enter your full name"
                            value={name}
                            onChangeText={setName}
                        />
                        {nameError ? <Text style={styles.errorText}>{nameError}</Text> : null}
                    </View>

                    <View style={styles.inputContainer}>
                        <Text style={styles.label}>Email</Text>
                        <TextInput
                            style={[styles.input, emailError ? styles.inputError : null]}
                            placeholder="Enter your email"
                            value={email}
                            onChangeText={setEmail}
                            keyboardType="email-address"
                            autoCapitalize="none"
                        />
                        {emailError ? <Text style={styles.errorText}>{emailError}</Text> : null}
                    </View>

                    <View style={styles.inputContainer}>
                        <Text style={styles.label}>Password</Text>
                        <TextInput
                            style={[styles.input, passwordError ? styles.inputError : null]}
                            placeholder="Create a password"
                            value={password}
                            onChangeText={setPassword}
                            secureTextEntry
                        />
                        {passwordError ? <Text style={styles.errorText}>{passwordError}</Text> : null}
                    </View>

                    <View style={styles.inputContainer}>
                        <Text style={styles.label}>Confirm Password</Text>
                        <TextInput
                            style={[styles.input, confirmPasswordError ? styles.inputError : null]}
                            placeholder="Confirm your password"
                            value={confirmPassword}
                            onChangeText={setConfirmPassword}
                            secureTextEntry
                        />
                        {confirmPasswordError ? <Text style={styles.errorText}>{confirmPasswordError}</Text> : null}
                    </View>

                    <View style={styles.accountTypeContainer}>
                        <Text style={styles.label}>Account Type</Text>
                        <View style={styles.accountTypeButtons}>
                            <TouchableOpacity
                                style={[
                                    styles.accountTypeButton,
                                    !isCook && styles.accountTypeButtonSelected
                                ]}
                                onPress={() => setIsCook(false)}
                            >
                                <Text
                                    style={[
                                        styles.accountTypeText,
                                        !isCook && styles.accountTypeTextSelected
                                    ]}
                                >
                                    Customer
                                </Text>
                            </TouchableOpacity>

                            <TouchableOpacity
                                style={[
                                    styles.accountTypeButton,
                                    isCook && styles.accountTypeButtonSelected
                                ]}
                                onPress={() => setIsCook(true)}
                            >
                                <Text
                                    style={[
                                        styles.accountTypeText,
                                        isCook && styles.accountTypeTextSelected
                                    ]}
                                >
                                    Cook
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>

                    <Button
                        title="Create Account"
                        onPress={handleRegister}
                        fullWidth
                        style={styles.button}
                    />
                </View>

                <View style={styles.footer}>
                    <Text style={styles.footerText}>Already have an account? </Text>
                    <TouchableOpacity onPress={() => router.push('/login')}>
                        <Text style={styles.footerLink}>Sign in</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    backButton: {
        padding: 16,
    },
    content: {
        flex: 1,
        padding: 24,
    },
    title: {
        fontSize: 28,
        fontWeight: 'bold',
        color: Colors.text,
        marginBottom: 8,
    },
    subtitle: {
        fontSize: 16,
        color: Colors.textLight,
        marginBottom: 24,
    },
    form: {
        marginBottom: 24,
    },
    inputContainer: {
        marginBottom: 16,
    },
    label: {
        fontSize: 14,
        fontWeight: '500',
        color: Colors.text,
        marginBottom: 8,
    },
    input: {
        backgroundColor: Colors.card,
        borderRadius: 8,
        padding: 12,
        fontSize: 16,
        color: Colors.text,
    },
    inputError: {
        borderWidth: 1,
        borderColor: Colors.error,
    },
    errorText: {
        color: Colors.error,
        fontSize: 12,
        marginTop: 4,
    },
    accountTypeContainer: {
        marginBottom: 24,
    },
    accountTypeButtons: {
        flexDirection: 'row',
        borderRadius: 8,
        overflow: 'hidden',
        backgroundColor: Colors.card,
    },
    accountTypeButton: {
        flex: 1,
        paddingVertical: 12,
        alignItems: 'center',
    },
    accountTypeButtonSelected: {
        backgroundColor: Colors.primary,
    },
    accountTypeText: {
        fontSize: 16,
        color: Colors.text,
    },
    accountTypeTextSelected: {
        color: Colors.white,
        fontWeight: '500',
    },
    button: {
        marginTop: 8,
    },
    footer: {
        flexDirection: 'row',
        justifyContent: 'center',
        marginTop: 'auto',
    },
    footerText: {
        fontSize: 14,
        color: Colors.textLight,
    },
    footerLink: {
        fontSize: 14,
        color: Colors.primary,
        fontWeight: '500',
    },
});