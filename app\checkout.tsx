import React, { useState } from 'react';
import { StyleSheet, Text, View, TextInput, ScrollView, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import Colors from '@/constants/colors';
import Button from '@/components/Button';
import { useCartStore } from '@/store/cartStore';

export default function CheckoutScreen() {
    const router = useRouter();
    const cartItems = useCartStore(state => state.items);
    const getTotal = useCartStore(state => state.getTotal);
    const clearCart = useCartStore(state => state.clearCart);

    const [address, setAddress] = useState('');
    const [addressError, setAddressError] = useState('');
    const [paymentMethod, setPaymentMethod] = useState('card');
    const [specialInstructions, setSpecialInstructions] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const subtotal = getTotal();
    const deliveryFee = 3.99;
    const total = subtotal + deliveryFee;

    const validateForm = () => {
        let isValid = true;

        if (!address.trim()) {
            setAddressError('Delivery address is required');
            isValid = false;
        } else {
            setAddressError('');
        }

        return isValid;
    };

    const handlePlaceOrder = () => {
        if (!validateForm()) return;

        setIsLoading(true);

        // Simulate API call
        setTimeout(() => {
            setIsLoading(false);

            // Success
            Alert.alert(
                'Order Placed',
                'Your order has been successfully placed!',
                [
                    {
                        text: 'View Orders',
                        onPress: () => {
                            clearCart();
                            router.push('/(tabs)/orders');
                        }
                    }
                ]
            );
        }, 1500);
    };

    return (
        <ScrollView style={styles.container}>
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Delivery Address</Text>
                <TextInput
                    style={[styles.input, addressError ? styles.inputError : null]}
                    placeholder="Enter your delivery address"
                    value={address}
                    onChangeText={setAddress}
                />
                {addressError ? <Text style={styles.errorText}>{addressError}</Text> : null}
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Payment Method</Text>
                <View style={styles.paymentOptions}>
                    <Button
                        title="Credit Card"
                        onPress={() => setPaymentMethod('card')}
                        variant={paymentMethod === 'card' ? 'primary' : 'outline'}
                        size="small"
                        style={styles.paymentButton}
                    />
                    <Button
                        title="Cash on Delivery"
                        onPress={() => setPaymentMethod('cash')}
                        variant={paymentMethod === 'cash' ? 'primary' : 'outline'}
                        size="small"
                        style={styles.paymentButton}
                    />
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Special Instructions</Text>
                <TextInput
                    style={[styles.input, styles.textArea]}
                    placeholder="Any special instructions for the cook or delivery person"
                    value={specialInstructions}
                    onChangeText={setSpecialInstructions}
                    multiline
                    numberOfLines={4}
                />
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Order Summary</Text>

                {cartItems.map((item) => (
                    <View key={item.mealId} style={styles.orderItem}>
                        <View style={styles.orderItemDetails}>
                            <Text style={styles.orderItemName}>{item.name}</Text>
                            <Text style={styles.orderItemQuantity}>x{item.quantity}</Text>
                        </View>
                        <Text style={styles.orderItemPrice}>
                            ${(item.price * item.quantity).toFixed(2)}
                        </Text>
                    </View>
                ))}

                <View style={styles.divider} />

                <View style={styles.summaryRow}>
                    <Text style={styles.summaryLabel}>Subtotal</Text>
                    <Text style={styles.summaryValue}>${subtotal.toFixed(2)}</Text>
                </View>

                <View style={styles.summaryRow}>
                    <Text style={styles.summaryLabel}>Delivery Fee</Text>
                    <Text style={styles.summaryValue}>${deliveryFee.toFixed(2)}</Text>
                </View>

                <View style={[styles.summaryRow, styles.totalRow]}>
                    <Text style={styles.totalLabel}>Total</Text>
                    <Text style={styles.totalValue}>${total.toFixed(2)}</Text>
                </View>
            </View>

            <View style={styles.footer}>
                <Button
                    title={`Place Order - $${total.toFixed(2)}`}
                    onPress={handlePlaceOrder}
                    fullWidth
                    loading={isLoading}
                />
            </View>
        </ScrollView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    section: {
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: Colors.border,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: Colors.text,
        marginBottom: 16,
    },
    input: {
        backgroundColor: Colors.white,
        borderWidth: 1,
        borderColor: Colors.border,
        borderRadius: 8,
        padding: 12,
        fontSize: 16,
        color: Colors.text,
    },
    inputError: {
        borderColor: Colors.error,
    },
    errorText: {
        color: Colors.error,
        fontSize: 12,
        marginTop: 4,
    },
    textArea: {
        height: 100,
        textAlignVertical: 'top',
    },
    paymentOptions: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    paymentButton: {
        flex: 1,
        marginHorizontal: 4,
    },
    orderItem: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 12,
    },
    orderItemDetails: {
        flex: 1,
    },
    orderItemName: {
        fontSize: 16,
        color: Colors.text,
    },
    orderItemQuantity: {
        fontSize: 14,
        color: Colors.textLight,
        marginTop: 2,
    },
    orderItemPrice: {
        fontSize: 16,
        fontWeight: '500',
        color: Colors.text,
    },
    divider: {
        height: 1,
        backgroundColor: Colors.border,
        marginVertical: 16,
    },
    summaryRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
    summaryLabel: {
        fontSize: 14,
        color: Colors.textLight,
    },
    summaryValue: {
        fontSize: 14,
        color: Colors.text,
    },
    totalRow: {
        marginTop: 8,
        paddingTop: 8,
        borderTopWidth: 1,
        borderTopColor: Colors.border,
    },
    totalLabel: {
        fontSize: 16,
        fontWeight: '600',
        color: Colors.text,
    },
    totalValue: {
        fontSize: 18,
        fontWeight: 'bold',
        color: Colors.primary,
    },
    footer: {
        padding: 16,
        marginBottom: 24,
    },
});