import Button from '@/components/Button';
import Colors from '@/constants/Colors';
import { useAuthStore } from '@/store/authStore';
import { useRouter } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import React, { useState } from 'react';
import { Alert, SafeAreaView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

export default function LoginScreen() {
    const router = useRouter();
    const login = useAuthStore(state => state.login);
    const isLoading = useAuthStore(state => state.isLoading);

    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [emailError, setEmailError] = useState('');
    const [passwordError, setPasswordError] = useState('');

    const validateForm = () => {
        let isValid = true;

        // Email validation
        if (!email.trim()) {
            setEmailError('Email is required');
            isValid = false;
        } else if (!/\S+@\S+\.\S+/.test(email)) {
            setEmailError('Email is invalid');
            isValid = false;
        } else {
            setEmailError('');
        }

        // Password validation
        if (!password) {
            setPasswordError('Password is required');
            isValid = false;
        } else if (password.length < 6) {
            setPasswordError('Password must be at least 6 characters');
            isValid = false;
        } else {
            setPasswordError('');
        }

        return isValid;
    };

    const handleLogin = async () => {
        if (!validateForm()) return;

        try {
            await login(email, password);
            router.replace('/(tabs)');
        } catch (error) {
            Alert.alert('Login Failed', 'Invalid email or password. Try <EMAIL> / password for <NAME_EMAIL> / password for cook.');
        }
    };

    const handleDemoLogin = async (userType: 'customer' | 'cook') => {
        try {
            if (userType === 'customer') {
                await login('<EMAIL>', 'password');
            } else {
                await login('<EMAIL>', 'password');
            }
            router.replace('/(tabs)');
        } catch (error) {
            Alert.alert('Login Failed', 'Something went wrong with demo login.');
        }
    };

    return (
        <SafeAreaView style={styles.container}>
            <TouchableOpacity
                style={styles.backButton}
                onPress={() => router.back()}
            >
                <ArrowLeft size={24} color={Colors.text} />
            </TouchableOpacity>

            <View style={styles.content}>
                <Text style={styles.title}>Welcome Back</Text>
                <Text style={styles.subtitle}>Sign in to your account</Text>

                <View style={styles.form}>
                    <View style={styles.inputContainer}>
                        <Text style={styles.label}>Email</Text>
                        <TextInput
                            style={[styles.input, emailError ? styles.inputError : null]}
                            placeholder="Enter your email"
                            value={email}
                            onChangeText={setEmail}
                            keyboardType="email-address"
                            autoCapitalize="none"
                        />
                        {emailError ? <Text style={styles.errorText}>{emailError}</Text> : null}
                    </View>

                    <View style={styles.inputContainer}>
                        <Text style={styles.label}>Password</Text>
                        <TextInput
                            style={[styles.input, passwordError ? styles.inputError : null]}
                            placeholder="Enter your password"
                            value={password}
                            onChangeText={setPassword}
                            secureTextEntry
                        />
                        {passwordError ? <Text style={styles.errorText}>{passwordError}</Text> : null}
                    </View>

                    <Button
                        title="Login"
                        onPress={handleLogin}
                        fullWidth
                        loading={isLoading}
                        style={styles.button}
                    />

                    <View style={styles.demoContainer}>
                        <Text style={styles.demoText}>For demo purposes:</Text>
                        <View style={styles.demoButtons}>
                            <Button
                                title="Login as Customer"
                                onPress={() => handleDemoLogin('customer')}
                                variant="outline"
                                size="small"
                                style={styles.demoButton}
                            />
                            <Button
                                title="Login as Cook"
                                onPress={() => handleDemoLogin('cook')}
                                variant="outline"
                                size="small"
                                style={styles.demoButton}
                            />
                        </View>
                    </View>
                </View>

                <View style={styles.footer}>
                    <Text style={styles.footerText}>Don't have an account? </Text>
                    <TouchableOpacity onPress={() => router.push('/register')}>
                        <Text style={styles.footerLink}>Sign up</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    backButton: {
        padding: 16,
    },
    content: {
        flex: 1,
        padding: 24,
    },
    title: {
        fontSize: 28,
        fontWeight: 'bold',
        color: Colors.text,
        marginBottom: 8,
    },
    subtitle: {
        fontSize: 16,
        color: Colors.textLight,
        marginBottom: 32,
    },
    form: {
        marginBottom: 24,
    },
    inputContainer: {
        marginBottom: 16,
    },
    label: {
        fontSize: 14,
        fontWeight: '500',
        color: Colors.text,
        marginBottom: 8,
    },
    input: {
        backgroundColor: Colors.card,
        borderRadius: 8,
        padding: 12,
        fontSize: 16,
        color: Colors.text,
    },
    inputError: {
        borderWidth: 1,
        borderColor: Colors.error,
    },
    errorText: {
        color: Colors.error,
        fontSize: 12,
        marginTop: 4,
    },
    button: {
        marginTop: 24,
    },
    demoContainer: {
        marginTop: 24,
        alignItems: 'center',
    },
    demoText: {
        fontSize: 14,
        color: Colors.textLight,
        marginBottom: 12,
    },
    demoButtons: {
        flexDirection: 'row',
        justifyContent: 'center',
        gap: 12,
    },
    demoButton: {
        flex: 1,
    },
    footer: {
        flexDirection: 'row',
        justifyContent: 'center',
        marginTop: 'auto',
    },
    footerText: {
        fontSize: 14,
        color: Colors.textLight,
    },
    footerLink: {
        fontSize: 14,
        color: Colors.primary,
        fontWeight: '500',
    },
});