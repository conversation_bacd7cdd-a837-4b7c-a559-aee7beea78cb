import React, { useState } from 'react';
import { StyleSheet, View, FlatList, Text, Pressable } from 'react-native';
import Colors from '@/constants/colors';
import { useAuthStore } from '@/store/authStore';
import { orders, cookOrders } from '@/mocks/orders';
import OrderItem from '@/components/OrderItem';

export default function OrdersScreen() {
    const user = useAuthStore(state => state.user);
    const isCook = user?.isCook;

    const [activeTab, setActiveTab] = useState<'active' | 'past'>('active');

    const userOrders = isCook ? cookOrders : orders;

    const activeOrders = userOrders.filter(order =>
        ['pending', 'confirmed', 'preparing', 'ready', 'out-for-delivery'].includes(order.status)
    );

    const pastOrders = userOrders.filter(order =>
        ['delivered', 'cancelled'].includes(order.status)
    );

    const displayOrders = activeTab === 'active' ? activeOrders : pastOrders;

    return (
        <View style={styles.container}>
            <View style={styles.tabs}>
                <Pressable
                    style={[styles.tab, activeTab === 'active' && styles.activeTab]}
                    onPress={() => setActiveTab('active')}
                >
                    <Text
                        style={[
                            styles.tabText,
                            activeTab === 'active' && styles.activeTabText
                        ]}
                    >
                        Active
                    </Text>
                </Pressable>
                <Pressable
                    style={[styles.tab, activeTab === 'past' && styles.activeTab]}
                    onPress={() => setActiveTab('past')}
                >
                    <Text
                        style={[
                            styles.tabText,
                            activeTab === 'past' && styles.activeTabText
                        ]}
                    >
                        Past
                    </Text>
                </Pressable>
            </View>

            <FlatList
                data={displayOrders}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => <OrderItem order={item} isCook={isCook} />}
                contentContainerStyle={styles.listContent}
                ListEmptyComponent={
                    <View style={styles.emptyContainer}>
                        <Text style={styles.emptyText}>No orders found</Text>
                        <Text style={styles.emptySubtext}>
                            {activeTab === 'active'
                                ? isCook
                                    ? "You don't have any active orders to prepare"
                                    : "You don't have any active orders"
                                : "You don't have any past orders"}
                        </Text>
                    </View>
                }
            />
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    tabs: {
        flexDirection: 'row',
        marginHorizontal: 16,
        marginVertical: 16,
        borderRadius: 8,
        backgroundColor: Colors.card,
        overflow: 'hidden',
    },
    tab: {
        flex: 1,
        paddingVertical: 12,
        alignItems: 'center',
    },
    activeTab: {
        backgroundColor: Colors.primary,
    },
    tabText: {
        fontSize: 16,
        color: Colors.text,
    },
    activeTabText: {
        color: Colors.white,
        fontWeight: '500',
    },
    listContent: {
        padding: 16,
    },
    emptyContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        padding: 32,
    },
    emptyText: {
        fontSize: 18,
        fontWeight: '600',
        color: Colors.text,
        marginBottom: 8,
    },
    emptySubtext: {
        fontSize: 14,
        color: Colors.textLight,
        textAlign: 'center',
    },
});