export interface User {
    id: string;
    name: string;
    email: string;
    phone: string;
    address: string;
    isCook: boolean;
}

export interface Cook {
    id: string;
    userId: string;
    name: string;
    bio: string;
    rating: number;
    reviewCount: number;
    profileImage: string;
    specialties: string[];
    deliveryRadius: number;
}

export interface Meal {
    id: string;
    cookId: string;
    name: string;
    description: string;
    price: number;
    image: string;
    category: string;
    ingredients: string[];
    allergens: string[];
    preparationTime: number; // in minutes
    availablePortions: number;
    isAvailable: boolean;
    rating: number;
    reviewCount: number;
}

export interface Order {
    id: string;
    userId: string;
    cookId: string;
    items: OrderItem[];
    status: OrderStatus;
    total: number;
    deliveryAddress: string;
    deliveryFee: number;
    placedAt: string; // ISO date string
    estimatedDelivery: string; // ISO date string
    specialInstructions?: string;
}

export interface OrderItem {
    mealId: string;
    name: string;
    price: number;
    quantity: number;
}

export type OrderStatus =
    | 'pending'
    | 'confirmed'
    | 'preparing'
    | 'ready'
    | 'out-for-delivery'
    | 'delivered'
    | 'cancelled';

export interface CartItem extends OrderItem {
    image: string;
}