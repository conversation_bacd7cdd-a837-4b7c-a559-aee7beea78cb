import React from 'react';
import { StyleSheet, Text, View, Pressable } from 'react-native';
import { useRouter } from 'expo-router';
import { ChevronRight, Clock } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { Order } from '@/types';

interface OrderItemProps {
    order: Order;
    isCook?: boolean;
}

export default function OrderItem({ order, isCook = false }: OrderItemProps) {
    const router = useRouter();

    const handlePress = () => {
        router.push(isCook ? `/cook/orders/${order.id}` : `/orders/${order.id}`);
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'pending':
                return Colors.warning;
            case 'confirmed':
            case 'preparing':
            case 'ready':
                return Colors.primary;
            case 'out-for-delivery':
                return Colors.secondary;
            case 'delivered':
                return Colors.success;
            case 'cancelled':
                return Colors.error;
            default:
                return Colors.textLight;
        }
    };

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleString('en-US', {
            month: 'short',
            day: 'numeric',
            hour: 'numeric',
            minute: '2-digit'
        });
    };

    const formatStatus = (status: string) => {
        return status.split('-').map(word =>
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
    };

    return (
        <Pressable
            style={styles.container}
            onPress={handlePress}
        >
            <View style={styles.header}>
                <Text style={styles.orderId}>Order #{order.id.slice(-4)}</Text>
                <View style={[styles.statusBadge, { backgroundColor: getStatusColor(order.status) }]}>
                    <Text style={styles.statusText}>{formatStatus(order.status)}</Text>
                </View>
            </View>

            <View style={styles.itemsList}>
                {order.items.map((item, index) => (
                    <Text key={index} style={styles.itemText}>
                        {item.quantity}x {item.name}
                    </Text>
                ))}
            </View>

            <View style={styles.footer}>
                <View style={styles.timeContainer}>
                    <Clock size={16} color={Colors.textLight} />
                    <Text style={styles.timeText}>
                        {isCook ? 'Ordered' : 'Expected'}: {formatDate(isCook ? order.placedAt : order.estimatedDelivery)}
                    </Text>
                </View>
                <View style={styles.priceContainer}>
                    <Text style={styles.priceLabel}>Total:</Text>
                    <Text style={styles.price}>${order.total.toFixed(2)}</Text>
                </View>
            </View>

            <ChevronRight size={20} color={Colors.textLight} style={styles.chevron} />
        </Pressable>
    );
}

const styles = StyleSheet.create({
    container: {
        backgroundColor: Colors.white,
        borderRadius: 12,
        padding: 16,
        marginBottom: 16,
        shadowColor: Colors.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 2,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 12,
    },
    orderId: {
        fontSize: 16,
        fontWeight: '600',
        color: Colors.text,
    },
    statusBadge: {
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
    },
    statusText: {
        color: Colors.white,
        fontSize: 12,
        fontWeight: '500',
    },
    itemsList: {
        marginBottom: 12,
    },
    itemText: {
        fontSize: 14,
        color: Colors.text,
        marginBottom: 4,
    },
    footer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderTopWidth: 1,
        borderTopColor: Colors.border,
        paddingTop: 12,
    },
    timeContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    timeText: {
        fontSize: 14,
        color: Colors.textLight,
        marginLeft: 4,
    },
    priceContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    priceLabel: {
        fontSize: 14,
        color: Colors.textLight,
        marginRight: 4,
    },
    price: {
        fontSize: 16,
        fontWeight: '600',
        color: Colors.primary,
    },
    chevron: {
        position: 'absolute',
        right: 16,
        top: '50%',
        marginTop: -10,
    },
});