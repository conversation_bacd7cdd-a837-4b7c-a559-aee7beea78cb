import CookMealCard from '@/components/CookMealCard';
import Colors from '@/constants/Colors';
import { meals } from '@/mocks/meals';
import { Meal } from '@/types';
import { useRouter } from 'expo-router';
import { Plus } from 'lucide-react-native';
import React, { useState } from 'react';
import { FlatList, Pressable, StyleSheet, Text, View } from 'react-native';

export default function CookMealsScreen() {
    const router = useRouter();
    const [cookMeals, setCookMeals] = useState<Meal[]>(meals.filter(meal => meal.cookId === '1'));

    const handleAddMeal = () => {
        router.push('/cook/meals/add');
    };

    const handleToggleAvailability = (id: string, isAvailable: boolean) => {
        setCookMeals(prevMeals =>
            prevMeals.map(meal =>
                meal.id === id ? { ...meal, isAvailable } : meal
            )
        );
    };

    return (
        <View style={styles.container}>
            <Pressable
                style={styles.addButton}
                onPress={handleAddMeal}
            >
                <Plus size={20} color={Colors.white} />
                <Text style={styles.addButtonText}>Add New Meal</Text>
            </Pressable>

            <FlatList
                data={cookMeals}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                    <CookMealCard
                        meal={item}
                        onToggleAvailability={handleToggleAvailability}
                    />
                )}
                contentContainerStyle={styles.listContent}
                ListEmptyComponent={
                    <View style={styles.emptyContainer}>
                        <Text style={styles.emptyText}>No meals added yet</Text>
                        <Text style={styles.emptySubtext}>Start by adding your first meal</Text>
                    </View>
                }
            />
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    addButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: Colors.primary,
        borderRadius: 8,
        padding: 12,
        margin: 16,
    },
    addButtonText: {
        color: Colors.white,
        fontSize: 16,
        fontWeight: '600',
        marginLeft: 8,
    },
    listContent: {
        padding: 16,
        paddingTop: 0,
    },
    emptyContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        padding: 32,
    },
    emptyText: {
        fontSize: 18,
        fontWeight: '600',
        color: Colors.text,
        marginBottom: 8,
    },
    emptySubtext: {
        fontSize: 14,
        color: Colors.textLight,
        textAlign: 'center',
    },
});