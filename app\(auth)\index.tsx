import Button from '@/components/Button';
import Colors from '@/constants/Colors';
import { useRouter } from 'expo-router';
import React from 'react';
import { Image, SafeAreaView, StyleSheet, Text, View } from 'react-native';

export default function WelcomeScreen() {
    const router = useRouter();

    return (
        <SafeAreaView style={styles.container}>
            <View style={styles.content}>
                <Image
                    source={{ uri: 'https://images.unsplash.com/photo-1576866209830-589e1bfbaa4d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80' }}
                    style={styles.image}
                />

                <View style={styles.textContainer}>
                    <Text style={styles.title}>FooDar</Text>
                    <Text style={styles.subtitle}>Homemade meals from local cooks</Text>
                    <Text style={styles.description}>
                        Discover delicious, authentic meals cooked with love by talented home chefs in your neighborhood.
                    </Text>
                </View>

                <View style={styles.buttonContainer}>
                    <Button
                        title="Login"
                        onPress={() => router.push('/login')}
                        variant="primary"
                        fullWidth
                        style={styles.button}
                    />
                    <Button
                        title="Register"
                        onPress={() => router.push('/register')}
                        variant="outline"
                        fullWidth
                        style={styles.button}
                    />
                </View>
            </View>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    content: {
        flex: 1,
        justifyContent: 'space-between',
        padding: 24,
    },
    image: {
        width: '100%',
        height: '45%',
        resizeMode: 'cover',
        borderRadius: 16,
        marginTop: 20,
    },
    textContainer: {
        marginVertical: 32,
    },
    title: {
        fontSize: 32,
        fontWeight: 'bold',
        color: Colors.text,
        textAlign: 'center',
        marginBottom: 8,
    },
    subtitle: {
        fontSize: 18,
        color: Colors.primary,
        textAlign: 'center',
        marginBottom: 16,
    },
    description: {
        fontSize: 16,
        color: Colors.textLight,
        textAlign: 'center',
        lineHeight: 24,
    },
    buttonContainer: {
        width: '100%',
        marginBottom: 24,
    },
    button: {
        marginBottom: 16,
    },
});