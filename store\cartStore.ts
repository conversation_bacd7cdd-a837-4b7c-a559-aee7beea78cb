import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { CartItem } from '@/types';
import { meals } from '@/mocks/meals';

interface CartState {
    items: CartItem[];
    cookId: string | null;
    addToCart: (mealId: string, quantity: number) => void;
    removeFromCart: (mealId: string) => void;
    updateQuantity: (mealId: string, quantity: number) => void;
    clearCart: () => void;
    getTotal: () => number;
    getItemCount: () => number;
}

export const useCartStore = create<CartState>()(
    persist(
        (set, get) => ({
            items: [],
            cookId: null,

            addToCart: (mealId, quantity) => {
                const meal = meals.find(m => m.id === mealId);
                if (!meal) return;

                set((state) => {
                    // If cart is empty or from same cook, add item
                    if (state.items.length === 0 || state.cookId === meal.cookId) {
                        const existingItem = state.items.find(item => item.mealId === mealId);

                        if (existingItem) {
                            // Update quantity if item already exists
                            return {
                                items: state.items.map(item =>
                                    item.mealId === mealId
                                        ? { ...item, quantity: item.quantity + quantity }
                                        : item
                                )
                            };
                        } else {
                            // Add new item
                            return {
                                items: [...state.items, {
                                    mealId,
                                    name: meal.name,
                                    price: meal.price,
                                    quantity,
                                    image: meal.image
                                }],
                                cookId: meal.cookId
                            };
                        }
                    } else {
                        // If trying to add item from different cook, replace cart
                        return {
                            items: [{
                                mealId,
                                name: meal.name,
                                price: meal.price,
                                quantity,
                                image: meal.image
                            }],
                            cookId: meal.cookId
                        };
                    }
                });
            },

            removeFromCart: (mealId) => {
                set((state) => ({
                    items: state.items.filter(item => item.mealId !== mealId),
                    cookId: state.items.length === 1 ? null : state.cookId
                }));
            },

            updateQuantity: (mealId, quantity) => {
                if (quantity <= 0) {
                    get().removeFromCart(mealId);
                    return;
                }

                set((state) => ({
                    items: state.items.map(item =>
                        item.mealId === mealId
                            ? { ...item, quantity }
                            : item
                    )
                }));
            },

            clearCart: () => {
                set({ items: [], cookId: null });
            },

            getTotal: () => {
                return get().items.reduce((total, item) => total + (item.price * item.quantity), 0);
            },

            getItemCount: () => {
                return get().items.reduce((count, item) => count + item.quantity, 0);
            }
        }),
        {
            name: 'cart-storage',
            storage: createJSONStorage(() => AsyncStorage),
        }
    )
);