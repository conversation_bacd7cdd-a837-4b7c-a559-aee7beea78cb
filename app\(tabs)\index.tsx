import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View, FlatList, Image, Pressable } from 'react-native';
import { useRouter } from 'expo-router';
import { ShoppingBag } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useAuthStore } from '@/store/authStore';
import { useCartStore } from '@/store/cartStore';
import { meals } from '@/mocks/meals';
import { cooks } from '@/mocks/cooks';
import MealCard from '@/components/MealCard';
import SearchBar from '@/components/SearchBar';

export default function HomeScreen() {
  const router = useRouter();
  const user = useAuthStore(state => state.user);
  const cartItemCount = useCartStore(state => state.getItemCount());

  const [searchQuery, setSearchQuery] = useState('');
  const [featuredMeals, setFeaturedMeals] = useState(meals.slice(0, 3));

  const isCook = user?.isCook;

  const handleCartPress = () => {
    router.push('/cart');
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      router.push({
        pathname: '/explore',
        params: { search: searchQuery }
      });
    }
  };

  const getCookName = (cookId: string) => {
    const cook = cooks.find(c => c.id === cookId);
    return cook ? cook.name : 'Unknown Cook';
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View>
          <Text style={styles.greeting}>Hello, {user?.name}</Text>
          <Text style={styles.subtitle}>
            {isCook ? 'Manage your meals and orders' : 'What would you like to eat today?'}
          </Text>
        </View>

        {!isCook && (
          <Pressable style={styles.cartButton} onPress={handleCartPress}>
            <ShoppingBag size={24} color={Colors.text} />
            {cartItemCount > 0 && (
              <View style={styles.badge}>
                <Text style={styles.badgeText}>{cartItemCount}</Text>
              </View>
            )}
          </Pressable>
        )}
      </View>

      <SearchBar
        value={searchQuery}
        onChangeText={setSearchQuery}
        onSubmit={handleSearch}
      />

      {isCook ? (
        <View style={styles.cookDashboard}>
          <Text style={styles.sectionTitle}>Cook Dashboard</Text>

          <View style={styles.statsContainer}>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>2</Text>
              <Text style={styles.statLabel}>Active Orders</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>6</Text>
              <Text style={styles.statLabel}>Total Meals</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>4.8</Text>
              <Text style={styles.statLabel}>Rating</Text>
            </View>
          </View>

          <Pressable
            style={styles.actionCard}
            onPress={() => router.push('/cook/meals/add')}
          >
            <View style={styles.actionContent}>
              <Text style={styles.actionTitle}>Add New Meal</Text>
              <Text style={styles.actionDescription}>Create a new dish to offer to customers</Text>
            </View>
            <Image
              source={{ uri: 'https://images.unsplash.com/photo-1556911220-e15b29be8c8f?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60' }}
              style={styles.actionImage}
            />
          </Pressable>

          <Pressable
            style={styles.actionCard}
            onPress={() => router.push('/cook-meals')}
          >
            <View style={styles.actionContent}>
              <Text style={styles.actionTitle}>Manage Your Meals</Text>
              <Text style={styles.actionDescription}>Update availability, prices, and details</Text>
            </View>
            <Image
              source={{ uri: 'https://images.unsplash.com/photo-1507048331197-7d4ac70811cf?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60' }}
              style={styles.actionImage}
            />
          </Pressable>
        </View>
      ) : (
        <FlatList
          data={featuredMeals}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => <MealCard meal={item} />}
          contentContainerStyle={styles.listContent}
          ListHeaderComponent={
            <>
              <View style={styles.bannerContainer}>
                <Image
                  source={{ uri: 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80' }}
                  style={styles.bannerImage}
                />
                <View style={styles.bannerOverlay}>
                  <Text style={styles.bannerTitle}>Support Local Cooks</Text>
                  <Text style={styles.bannerText}>Enjoy authentic homemade meals delivered to your door</Text>
                </View>
              </View>

              <Text style={styles.sectionTitle}>Featured Meals</Text>
            </>
          }
          ListFooterComponent={
            <Pressable
              style={styles.exploreButton}
              onPress={() => router.push('/explore')}
            >
              <Text style={styles.exploreButtonText}>Explore All Meals</Text>
            </Pressable>
          }
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textLight,
    marginTop: 4,
  },
  cartButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.card,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badge: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: Colors.primary,
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: Colors.white,
    fontSize: 12,
    fontWeight: 'bold',
  },
  bannerContainer: {
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 12,
    overflow: 'hidden',
    height: 160,
  },
  bannerImage: {
    width: '100%',
    height: '100%',
  },
  bannerOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.4)',
    padding: 16,
    justifyContent: 'flex-end',
  },
  bannerTitle: {
    color: Colors.white,
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  bannerText: {
    color: Colors.white,
    fontSize: 14,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
    marginHorizontal: 16,
    marginTop: 24,
    marginBottom: 16,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
  exploreButton: {
    backgroundColor: Colors.card,
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 16,
  },
  exploreButtonText: {
    color: Colors.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  cookDashboard: {
    flex: 1,
    padding: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  statCard: {
    flex: 1,
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginHorizontal: 4,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.textLight,
    textAlign: 'center',
  },
  actionCard: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
    flexDirection: 'row',
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  actionContent: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  actionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  actionDescription: {
    fontSize: 14,
    color: Colors.textLight,
  },
  actionImage: {
    width: 100,
    height: '100%',
    resizeMode: 'cover',
  },
});