import Colors from '@/constants/Colors';
import { Meal } from '@/types';
import { useRouter } from 'expo-router';
import { Edit2 } from 'lucide-react-native';
import React from 'react';
import { Image, Pressable, StyleSheet, Switch, Text, View } from 'react-native';

interface CookMealCardProps {
    meal: Meal;
    onToggleAvailability: (id: string, isAvailable: boolean) => void;
}

export default function CookMealCard({ meal, onToggleAvailability }: CookMealCardProps) {
    const router = useRouter();

    const handleEditPress = () => {
        router.push(`/cook/meals/edit/${meal.id}`);
    };

    const handleToggleSwitch = (value: boolean) => {
        onToggleAvailability(meal.id, value);
    };

    return (
        <View style={styles.container}>
            <Image source={{ uri: meal.image }} style={styles.image} />

            <View style={styles.content}>
                <View style={styles.header}>
                    <Text style={styles.name} numberOfLines={1}>{meal.name}</Text>
                    <Pressable
                        style={styles.editButton}
                        onPress={handleEditPress}
                    >
                        <Edit2 size={16} color={Colors.primary} />
                    </Pressable>
                </View>

                <Text style={styles.price}>${meal.price.toFixed(2)}</Text>

                <View style={styles.footer}>
                    <View style={styles.availabilityContainer}>
                        <Text style={styles.availabilityLabel}>
                            Available ({meal.availablePortions} portions)
                        </Text>
                        <Switch
                            value={meal.isAvailable}
                            onValueChange={handleToggleSwitch}
                            trackColor={{ false: Colors.border, true: Colors.primary }}
                            thumbColor={Colors.white}
                        />
                    </View>
                </View>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        backgroundColor: Colors.white,
        borderRadius: 12,
        overflow: 'hidden',
        marginBottom: 16,
        shadowColor: Colors.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 2,
        flexDirection: 'row',
        height: 100,
    },
    image: {
        width: 100,
        height: '100%',
        resizeMode: 'cover',
    },
    content: {
        flex: 1,
        padding: 12,
        justifyContent: 'space-between',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    name: {
        fontSize: 16,
        fontWeight: '600',
        color: Colors.text,
        flex: 1,
        marginRight: 8,
    },
    editButton: {
        padding: 4,
    },
    price: {
        fontSize: 16,
        fontWeight: '600',
        color: Colors.primary,
        marginVertical: 4,
    },
    footer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    availabilityContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        flex: 1,
    },
    availabilityLabel: {
        fontSize: 14,
        color: Colors.textLight,
    },
});