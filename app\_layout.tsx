import FontAwesome from "@expo/vector-icons/FontAwesome";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { useEffect } from "react";
import { StatusBar } from "expo-status-bar";

export const unstable_settings = {
    initialRouteName: "(tabs)",
};

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
    const [loaded, error] = useFonts({
        ...FontAwesome.font,
    });

    useEffect(() => {
        if (error) {
            console.error(error);
            throw error;
        }
    }, [error]);

    useEffect(() => {
        if (loaded) {
            SplashScreen.hideAsync();
        }
    }, [loaded]);

    if (!loaded) {
        return null;
    }

    return <RootLayoutNav />;
}

function RootLayoutNav() {
    return (
        <>
            <StatusBar style="dark" />
            <Stack>
                <Stack.Screen name="(auth)" options={{ headerShown: false }} />
                <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
                <Stack.Screen name="meal/[id]" options={{ title: "Meal Details" }} />
                <Stack.Screen name="cart" options={{ title: "Your Cart" }} />
                <Stack.Screen name="checkout" options={{ title: "Checkout" }} />
                <Stack.Screen name="orders/[id]" options={{ title: "Order Details" }} />
                <Stack.Screen name="cook/meals/add" options={{ title: "Add New Meal" }} />
                <Stack.Screen name="cook/meals/edit/[id]" options={{ title: "Edit Meal" }} />
                <Stack.Screen name="cook/orders/[id]" options={{ title: "Order Details" }} />
                <Stack.Screen name="cook/earnings" options={{ title: "Earnings Dashboard" }} />
                <Stack.Screen name="cook-profile/[id]" options={{ title: "Cook Profile" }} />
                <Stack.Screen name="user-profile/[id]" options={{ title: "User Profile" }} />
            </Stack>
        </>
    );
}