import React from 'react';
import { StyleSheet, Text, View, Image, ScrollView, Pressable, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { Trash2, Plus, Minus } from 'lucide-react-native';
import Colors from '@/constants/colors';
import Button from '@/components/Button';
import { useCartStore } from '@/store/cartStore';

export default function CartScreen() {
    const router = useRouter();
    const cartItems = useCartStore(state => state.items);
    const updateQuantity = useCartStore(state => state.updateQuantity);
    const removeFromCart = useCartStore(state => state.removeFromCart);
    const clearCart = useCartStore(state => state.clearCart);
    const getTotal = useCartStore(state => state.getTotal);

    const handleIncrement = (mealId: string, currentQuantity: number) => {
        updateQuantity(mealId, currentQuantity + 1);
    };

    const handleDecrement = (mealId: string, currentQuantity: number) => {
        if (currentQuantity > 1) {
            updateQuantity(mealId, currentQuantity - 1);
        } else {
            handleRemove(mealId);
        }
    };

    const handleRemove = (mealId: string) => {
        Alert.alert(
            'Remove Item',
            'Are you sure you want to remove this item from your cart?',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Remove',
                    style: 'destructive',
                    onPress: () => removeFromCart(mealId)
                }
            ]
        );
    };

    const handleClearCart = () => {
        if (cartItems.length === 0) return;

        Alert.alert(
            'Clear Cart',
            'Are you sure you want to clear your cart?',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Clear',
                    style: 'destructive',
                    onPress: () => clearCart()
                }
            ]
        );
    };

    const handleCheckout = () => {
        router.push('/checkout');
    };

    const subtotal = getTotal();
    const deliveryFee = 3.99;
    const total = subtotal + deliveryFee;

    return (
        <View style={styles.container}>
            <ScrollView style={styles.itemsContainer}>
                {cartItems.length === 0 ? (
                    <View style={styles.emptyContainer}>
                        <Text style={styles.emptyText}>Your cart is empty</Text>
                        <Text style={styles.emptySubtext}>Add some delicious meals to get started</Text>
                        <Button
                            title="Browse Meals"
                            onPress={() => router.push('/explore')}
                            variant="outline"
                            style={styles.browseButton}
                        />
                    </View>
                ) : (
                    <>
                        <View style={styles.header}>
                            <Text style={styles.title}>Your Cart</Text>
                            <Pressable onPress={handleClearCart}>
                                <Text style={styles.clearText}>Clear All</Text>
                            </Pressable>
                        </View>

                        {cartItems.map((item) => (
                            <View key={item.mealId} style={styles.cartItem}>
                                <Image source={{ uri: item.image }} style={styles.itemImage} />

                                <View style={styles.itemDetails}>
                                    <Text style={styles.itemName}>{item.name}</Text>
                                    <Text style={styles.itemPrice}>${item.price.toFixed(2)}</Text>

                                    <View style={styles.itemActions}>
                                        <View style={styles.quantityContainer}>
                                            <Pressable
                                                style={styles.quantityButton}
                                                onPress={() => handleDecrement(item.mealId, item.quantity)}
                                            >
                                                <Minus size={16} color={Colors.text} />
                                            </Pressable>

                                            <Text style={styles.quantity}>{item.quantity}</Text>

                                            <Pressable
                                                style={styles.quantityButton}
                                                onPress={() => handleIncrement(item.mealId, item.quantity)}
                                            >
                                                <Plus size={16} color={Colors.text} />
                                            </Pressable>
                                        </View>

                                        <Pressable
                                            style={styles.removeButton}
                                            onPress={() => handleRemove(item.mealId)}
                                        >
                                            <Trash2 size={18} color={Colors.error} />
                                        </Pressable>
                                    </View>
                                </View>
                            </View>
                        ))}
                    </>
                )}
            </ScrollView>

            {cartItems.length > 0 && (
                <View style={styles.footer}>
                    <View style={styles.summaryContainer}>
                        <View style={styles.summaryRow}>
                            <Text style={styles.summaryLabel}>Subtotal</Text>
                            <Text style={styles.summaryValue}>${subtotal.toFixed(2)}</Text>
                        </View>

                        <View style={styles.summaryRow}>
                            <Text style={styles.summaryLabel}>Delivery Fee</Text>
                            <Text style={styles.summaryValue}>${deliveryFee.toFixed(2)}</Text>
                        </View>

                        <View style={[styles.summaryRow, styles.totalRow]}>
                            <Text style={styles.totalLabel}>Total</Text>
                            <Text style={styles.totalValue}>${total.toFixed(2)}</Text>
                        </View>
                    </View>

                    <Button
                        title="Proceed to Checkout"
                        onPress={handleCheckout}
                        fullWidth
                    />
                </View>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    itemsContainer: {
        flex: 1,
    },
    emptyContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        padding: 32,
        marginTop: 80,
    },
    emptyText: {
        fontSize: 20,
        fontWeight: '600',
        color: Colors.text,
        marginBottom: 8,
    },
    emptySubtext: {
        fontSize: 16,
        color: Colors.textLight,
        textAlign: 'center',
        marginBottom: 24,
    },
    browseButton: {
        width: 200,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
    },
    title: {
        fontSize: 20,
        fontWeight: '600',
        color: Colors.text,
    },
    clearText: {
        fontSize: 14,
        color: Colors.error,
    },
    cartItem: {
        flexDirection: 'row',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: Colors.border,
    },
    itemImage: {
        width: 80,
        height: 80,
        borderRadius: 8,
    },
    itemDetails: {
        flex: 1,
        marginLeft: 16,
    },
    itemName: {
        fontSize: 16,
        fontWeight: '500',
        color: Colors.text,
        marginBottom: 4,
    },
    itemPrice: {
        fontSize: 16,
        fontWeight: '600',
        color: Colors.primary,
        marginBottom: 8,
    },
    itemActions: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    quantityContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: Colors.border,
        borderRadius: 4,
    },
    quantityButton: {
        padding: 6,
    },
    quantity: {
        fontSize: 14,
        fontWeight: '500',
        color: Colors.text,
        paddingHorizontal: 8,
    },
    removeButton: {
        padding: 8,
    },
    footer: {
        padding: 16,
        borderTopWidth: 1,
        borderTopColor: Colors.border,
        backgroundColor: Colors.white,
    },
    summaryContainer: {
        marginBottom: 16,
    },
    summaryRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
    summaryLabel: {
        fontSize: 14,
        color: Colors.textLight,
    },
    summaryValue: {
        fontSize: 14,
        color: Colors.text,
    },
    totalRow: {
        marginTop: 8,
        paddingTop: 8,
        borderTopWidth: 1,
        borderTopColor: Colors.border,
    },
    totalLabel: {
        fontSize: 16,
        fontWeight: '600',
        color: Colors.text,
    },
    totalValue: {
        fontSize: 18,
        fontWeight: 'bold',
        color: Colors.primary,
    },
});