import Colors from '@/constants/Colors';
import { Meal } from '@/types';
import { useRouter } from 'expo-router';
import { Star } from 'lucide-react-native';
import React from 'react';
import { Image, Pressable, StyleSheet, Text, View } from 'react-native';

interface MealCardProps {
    meal: Meal;
}

export default function MealCard({ meal }: MealCardProps) {
    const router = useRouter();

    const handlePress = () => {
        router.push(`/meal/${meal.id}`);
    };

    return (
        <Pressable
            style={styles.container}
            onPress={handlePress}
        >
            <Image source={{ uri: meal.image }} style={styles.image} />
            <View style={styles.content}>
                <Text style={styles.name} numberOfLines={1}>{meal.name}</Text>
                <Text style={styles.description} numberOfLines={2}>{meal.description}</Text>
                <View style={styles.footer}>
                    <View style={styles.ratingContainer}>
                        <Star size={16} color={Colors.warning} fill={Colors.warning} />
                        <Text style={styles.rating}>{meal.rating.toFixed(1)}</Text>
                        <Text style={styles.reviews}>({meal.reviewCount})</Text>
                    </View>
                    <Text style={styles.price}>${meal.price.toFixed(2)}</Text>
                </View>
            </View>
        </Pressable>
    );
}

const styles = StyleSheet.create({
    container: {
        backgroundColor: Colors.white,
        borderRadius: 12,
        overflow: 'hidden',
        marginBottom: 16,
        shadowColor: Colors.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 2,
    },
    image: {
        width: '100%',
        height: 160,
        resizeMode: 'cover',
    },
    content: {
        padding: 12,
    },
    name: {
        fontSize: 18,
        fontWeight: '600',
        color: Colors.text,
        marginBottom: 4,
    },
    description: {
        fontSize: 14,
        color: Colors.textLight,
        marginBottom: 8,
        lineHeight: 20,
    },
    footer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: 4,
    },
    ratingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    rating: {
        fontSize: 14,
        fontWeight: '500',
        color: Colors.text,
        marginLeft: 4,
    },
    reviews: {
        fontSize: 14,
        color: Colors.textLight,
        marginLeft: 2,
    },
    price: {
        fontSize: 16,
        fontWeight: '600',
        color: Colors.primary,
    },
});